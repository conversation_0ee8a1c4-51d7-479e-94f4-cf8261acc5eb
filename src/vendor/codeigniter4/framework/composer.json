{"name": "codeigniter4/framework", "description": "The CodeIgniter framework v4", "license": "MIT", "type": "project", "homepage": "https://codeigniter.com", "support": {"forum": "https://forum.codeigniter.com/", "source": "https://github.com/codeigniter4/CodeIgniter4", "slack": "https://codeigniterchat.slack.com"}, "require": {"php": "^8.1", "ext-intl": "*", "ext-mbstring": "*", "laminas/laminas-escaper": "^2.13", "psr/log": "^3.0"}, "require-dev": {"codeigniter/coding-standard": "^1.7", "fakerphp/faker": "^1.9", "friendsofphp/php-cs-fixer": "^3.47.1", "kint-php/kint": "^5.0.4", "mikey179/vfsstream": "^1.6", "nexusphp/cs-config": "^3.6", "phpunit/phpunit": "^10.5.16", "predis/predis": "^1.1 || ^2.0"}, "suggest": {"ext-curl": "If you use CURLRequest class", "ext-dom": "If you use TestResponse", "ext-exif": "If you run Image class tests", "ext-fileinfo": "Improves mime type detection for files", "ext-gd": "If you use Image class GDHandler", "ext-imagick": "If you use Image class ImageMagickHandler", "ext-libxml": "If you use TestResponse", "ext-memcache": "If you use Cache class MemcachedHandler with Memcache", "ext-memcached": "If you use Cache class MemcachedHandler with Memcached", "ext-mysqli": "If you use MySQL", "ext-oci8": "If you use Oracle Database", "ext-pgsql": "If you use PostgreSQL", "ext-readline": "Improves CLI::input() usability", "ext-redis": "If you use Cache class RedisHandler", "ext-simplexml": "If you format XML", "ext-sodium": "If you use Encryption SodiumHandler", "ext-sqlite3": "If you use SQLite3", "ext-sqlsrv": "If you use SQL Server", "ext-xdebug": "If you use CIUnitTestCase::assertHeaderEmitted()"}, "autoload": {"psr-4": {"CodeIgniter\\": "system/"}, "exclude-from-classmap": ["**/Database/Migrations/**"]}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "scripts": {"test": "phpunit"}}