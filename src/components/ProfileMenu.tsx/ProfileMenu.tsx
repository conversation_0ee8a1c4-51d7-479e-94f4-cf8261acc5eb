import { Link } from 'react-router-dom';

import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';

interface ProfileMenuProps {
  anchorEl: HTMLElement | null; // Define anchorEl as HTMLElement | null
  onClose: () => void;
}

function ProfileMenu({ anchorEl, onClose }: ProfileMenuProps) {
  const open = Boolean(anchorEl);

  const handleClose = () => {
    onClose();
  };

  return (
    <div>
      <Menu
        id="basic-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
      >
        <MenuItem onClick={handleClose}>
          <Link to="/profile" style={{ textDecoration: 'none', color: '#000' }}>
            Profile
          </Link>
        </MenuItem>
        <MenuItem onClick={handleClose}>
          <Link to="/logout" style={{ textDecoration: 'none', color: '#000' }}>
            Logout
          </Link>
        </MenuItem>
      </Menu>
    </div>
  );
}

export default ProfileMenu;
