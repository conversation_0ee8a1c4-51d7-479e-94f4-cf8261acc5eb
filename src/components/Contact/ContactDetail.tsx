import {
  Box,
  Card,
  CircularProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material';

import { useReadContact } from '../../hooks/contact';
import { IContact, IReadContact } from '../../types/contact';

interface ContactDetailProps {
  data?: IContact | null;
}

const ContactDetail = ({ data }: ContactDetailProps) => {
  const id = data?.id;
  const {
    data: contact,
    isLoading,
    isError,
  } = useReadContact({ id } as IReadContact);

  if (isError) {
    return <div>Error fetching Contact</div>;
  }

  return (
    <div className="content">
      <div className="content-inner">
        <Box style={{ padding: '10px', margin: '10px' }}>
          <Typography variant="h5">Contact Details</Typography>
        </Box>

        {isLoading ? (
          <Box
            sx={{ display: 'flex', width: '100%', justifyContent: 'center' }}
          >
            <CircularProgress />
          </Box>
        ) : (
          <Card style={{ margin: '10px' }}>
            <TableContainer component={Paper}>
              <Table>
                <TableBody>
                  <TableRow>
                    <TableCell>
                      <strong>Company Name:</strong>
                    </TableCell>
                    <TableCell>{contact?.cname}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <strong>Address:</strong>
                    </TableCell>
                    <TableCell>{contact?.address}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <strong>Phone:</strong>
                    </TableCell>
                    <TableCell>{contact?.phone}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell colSpan={2}>
                      <strong>Contact Person (Operations):</strong>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <strong>Name:</strong>
                    </TableCell>
                    <TableCell>
                      {contact?.cpo_fname} {contact?.cpo_lname}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <strong>Phone 1:</strong>
                    </TableCell>
                    <TableCell>{contact?.cpo_phone1}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <strong>Phone 2:</strong>
                    </TableCell>
                    <TableCell>{contact?.cpo_phone2}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell colSpan={2}>
                      <strong>Contact Person (Accounts):</strong>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <strong>Name:</strong>
                    </TableCell>
                    <TableCell>
                      {contact?.cpa_fname} {contact?.cpa_lname}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <strong>Phone 1:</strong>
                    </TableCell>
                    <TableCell>{contact?.cpa_phone1}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <strong>Phone 2:</strong>
                    </TableCell>
                    <TableCell>{contact?.cpa_phone2}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Card>
        )}
      </div>
    </div>
  );
};

export default ContactDetail;
