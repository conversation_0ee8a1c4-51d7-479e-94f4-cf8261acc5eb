import { Controller, useFormContext } from 'react-hook-form';

import { CircularProgress } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';

interface SelectionDropdownProps {
  name: string;
  options: any;
  label: string;
  renderLabel: (option: any) => string;
  setSelectedValue: (option: any) => any;
  displayLabel: string;
  loading?: boolean;
  error?: boolean;
  helperText?: string;
  disabled?: boolean;
}

export default function SelectionDropdown({
  name,
  options,
  label,
  renderLabel,
  setSelectedValue,
  displayLabel,
  loading = false,
  error = false,
  helperText = '',
  disabled = false,
}: SelectionDropdownProps) {
  const { control } = useFormContext();

  const uniqueOptions = Array.isArray(options)
    ? options.filter(
        (option, index, self) =>
          self.findIndex(
            o =>
              renderLabel(o).toLowerCase() === renderLabel(option).toLowerCase()
          ) === index
      )
    : options;

  return (
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <Autocomplete
          disabled={disabled}
          options={uniqueOptions}
          loading={loading}
          filterOptions={(optionsFilter, state) =>
            optionsFilter.filter(optionFilter =>
              renderLabel(optionFilter)
                .toLowerCase()
                .includes(state.inputValue.toLowerCase())
            )
          }
          isOptionEqualToValue={(option, value) => option.id === value.id}
          getOptionLabel={renderLabel}
          value={
            uniqueOptions.find(
              (option: any) => option[displayLabel] === field.value
            ) || null
          }
          onChange={(_, newValue) => {
            field.onChange(newValue ? newValue[displayLabel] : '');
            setSelectedValue(newValue);
          }}
          renderInput={params => (
            <TextField
              {...params}
              label={label}
              error={error}
              helperText={helperText}
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {loading ? (
                      <CircularProgress color="inherit" size={20} />
                    ) : null}
                    {params.InputProps.endAdornment}
                  </>
                ),
              }}
            />
          )}
        />
      )}
    />
  );
}
