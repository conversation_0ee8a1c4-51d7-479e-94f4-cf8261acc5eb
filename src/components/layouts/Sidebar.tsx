import React, { useState } from 'react';

import { enqueueSnackbar } from 'notistack';
import { Link, useLocation, useNavigate } from 'react-router-dom';

import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import LogoutIcon from '@mui/icons-material/Logout';
import ManageAccountsIcon from '@mui/icons-material/ManageAccounts';
import {
  Box,
  Collapse,
  FormControl,
  InputLabel,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Select,
  Typography,
} from '@mui/material';

import apiClient from '@constants/axios';
import { getFilteredMenu, IMenuItem } from '@constants/menu';

import { useSetDbYear } from '../../hooks/misc';

const Sidebar: React.FC<{ open: boolean }> = ({ open }) => {
  const navigate = useNavigate();
  const [dbYear, setDbYear] = useState(localStorage.getItem('year'));

  // Get user level from session storage
  const storedUserLevel = sessionStorage.getItem('userlevel');
  const userLevel = parseInt(storedUserLevel || '2', 10);

  const filteredMenu = getFilteredMenu(userLevel);

  const { mutate: updateDbYearMutate } = useSetDbYear();

  const changeDbYear = (event: any) => {
    const year = String(event.target.value);
    setDbYear(year);
    localStorage.setItem('year', year);
    updateDbYearMutate(
      { year },
      {
        onSuccess: () => {
          window.location.reload();
        },
        onError: () => {
          enqueueSnackbar('Failed to Update year', { variant: 'error' });
        },
      }
    );
  };

  const startYear = 2020;
  const dbYearData = [];
  const currentMonth = new Date().getMonth();
  let currentYear = new Date().getFullYear();
  if (currentMonth === 0 || currentMonth === 1 || currentMonth === 2) {
    // eslint-disable-next-line no-plusplus
    currentYear--;
  }
  // eslint-disable-next-line no-plusplus
  for (let year = startYear; year <= currentYear + 1; year++) {
    dbYearData.push({ id: year, year });
  }

  const handleLogout = async () => {
    try {
      const response = await apiClient.get('/logout');
      if (response) {
        sessionStorage.removeItem('isAuthenticated');
        sessionStorage.removeItem('userlevel');
        navigate('/login');
      } else {
        console.error('Logout failed');
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <Box
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        width: '100%',
      }}
    >
      <List
        sx={{
          flexGrow: 1,
          overflowY: 'auto',
          overflowX: 'hidden',
          width: '100%',
          paddingRight: '6px',
          '&::-webkit-scrollbar': {
            width: '6px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(0,0,0,.2)',
            borderRadius: '3px',
          },
          '&::-webkit-scrollbar-track': {
            marginTop: '4px',
            marginBottom: '4px',
          },
        }}
      >
        {filteredMenu.map((item: IMenuItem) => (
          <Box key={item?.title} sx={{ width: '100%' }}>
            <MenuItem item={item} open={open} />
          </Box>
        ))}
      </List>
      <List
        sx={{
          borderTop: '1px solid rgba(0, 0, 0, 0.12)',
          width: '100%',
          paddingRight: '6px', // Add padding to avoid scrollbar overlap
        }}
      >
        <ListItemButton style={{ paddingLeft: '1rem', paddingRight: '1rem' }}>
          <ListItemIcon style={{ minWidth: '40px' }}>
            <CalendarTodayIcon />
          </ListItemIcon>
          <FormControl variant="outlined" style={{ width: '160px' }}>
            <InputLabel htmlFor="outlined-native-simple">Year</InputLabel>
            <Select
              native
              value={dbYear}
              onChange={changeDbYear}
              label="Year"
              inputProps={{
                name: 'year',
                id: 'outlined-native-simple',
              }}
            >
              {dbYearData.map((t: any) => (
                <option key={t.id} value={t.id}>
                  {t.year}
                </option>
              ))}
            </Select>
          </FormControl>
        </ListItemButton>

        <MenuItem
          key="Profile"
          item={{
            title: 'Profile',
            link: '/profile',
            icon: <ManageAccountsIcon />,
          }}
          open={open}
        />
        <ListItemButton
          onClick={handleLogout}
          style={{ paddingLeft: '1rem', paddingRight: '1rem' }}
        >
          <ListItemIcon style={{ minWidth: '40px' }}>
            <LogoutIcon />
          </ListItemIcon>
          <ListItemText
            primary={<Typography variant="body2">Logout</Typography>}
            sx={{ marginLeft: '8px' }}
          />
        </ListItemButton>
      </List>
    </Box>
  );
};

const MenuItem = ({ item, open }: { item: IMenuItem; open: boolean }) => {
  const Component = hasChildren(item) ? MultiLevel : SingleLevel;
  return <Component item={item} open={open} />;
};

const SingleLevel = ({ item }: { item: IMenuItem }) => {
  const location = useLocation();
  const { pathname } = location;

  return (
    <ListItemButton
      key={item?.title}
      selected={item?.link === pathname}
      sx={{
        paddingLeft: '1rem',
        paddingRight: '1rem',
        width: '100%',
      }}
    >
      <ListItemIcon sx={{ minWidth: '40px' }}>{item?.icon}</ListItemIcon>
      {item?.link ? (
        <Link
          to={item?.link ?? ''}
          style={{
            textDecoration: 'none',
            color: 'black',
            width: '100%',
            overflow: 'hidden',
          }}
        >
          <ListItemText
            primary={<Typography variant="body2">{item?.title}</Typography>}
            sx={{
              marginLeft: '4px',
              padding: 0,
              margin: 0,
              '& .MuiTypography-root': {
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
            }}
          />
        </Link>
      ) : (
        <ListItemText
          primary={<Typography variant="body2">{item?.title}</Typography>}
          sx={{
            marginLeft: '4px',
            padding: 0,
            margin: 0,
            '& .MuiTypography-root': {
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
          }}
        />
      )}
    </ListItemButton>
  );
};

const MultiLevel = ({
  item,
  open: isSidebarOpen,
}: {
  item: IMenuItem;
  open: boolean;
}) => {
  const { items: children } = item;
  const [open, setOpen] = React.useState(false);

  const handleClick = () => {
    setOpen(prev => !prev);
  };

  return (
    <>
      <ListItemButton
        key={item?.title}
        onClick={handleClick}
        sx={{
          paddingLeft: '1rem',
          paddingRight: '3rem', // Increased right padding to accommodate arrow
          position: 'relative',
          width: '100%',
        }}
      >
        <ListItemIcon sx={{ minWidth: '40px' }}>{item?.icon}</ListItemIcon>
        <ListItemText
          primary={<Typography variant="body2">{item?.title}</Typography>}
          sx={{
            marginLeft: '4px',
            '& .MuiTypography-root': {
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            },
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            right: '8px',
            top: '50%',
            transform: 'translateY(-50%)',
            display: isSidebarOpen ? 'flex' : 'none', // Only show icons when sidebar is open
            justifyContent: 'center',
            width: '24px',
            zIndex: 1,
          }}
        >
          {open ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        </Box>
      </ListItemButton>
      <Collapse in={open} timeout="auto" unmountOnExit>
        <List
          component="div"
          disablePadding
          sx={{
            position: 'relative',
            width: '100%',
            paddingRight: '6px', // Add padding to avoid scrollbar overlap
            '&:before': {
              content: '""',
              position: 'absolute',
              left: '29px',
              top: 0,
              bottom: 0,
              width: '2px',
              bgcolor: 'rgba(0, 0, 0, 0.12)',
            },
          }}
        >
          {children?.map((child: IMenuItem) => (
            <Box
              key={child.title}
              sx={{
                paddingLeft: '1rem',
                position: 'relative',
                width: '100%',
              }}
            >
              {isSidebarOpen ? (
                <MenuItem
                  key={child?.title}
                  item={child}
                  open={isSidebarOpen}
                />
              ) : null}
            </Box>
          ))}
        </List>
      </Collapse>
    </>
  );
};

export function hasChildren(item: IMenuItem) {
  const { items: children } = item;

  if (children === undefined) {
    return false;
  }

  if (!Array.isArray(children)) {
    return false;
  }

  if (children.length === 0) {
    return false;
  }

  return true;
}

export default Sidebar;
