import { useFormContext } from 'react-hook-form';

import { Card, Grid, TextField, Typography } from '@mui/material';

function AddGST() {
  const { register } = useFormContext();

  return (
    <div className="content">
      <div className="content-inner">
        <Card style={{ marginTop: '20px', padding: '10px' }}>
          <Typography variant="h5" style={{ marginBottom: '20px' }}>
            Add New GST Tax
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                id="tax"
                variant="outlined"
                label="Tax"
                {...register('tax')}
                fullWidth
                margin="normal"
                type="number"
              />
            </Grid>
          </Grid>
        </Card>
      </div>
    </div>
  );
}

export default AddGST;
