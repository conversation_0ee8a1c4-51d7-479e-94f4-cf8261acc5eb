import {
  Box,
  Card,
  CircularProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material';

import { useReadStaff } from '../../../hooks/controlpanel/staff';
import { IReadStaff, IStaff } from '../../../types/controlpanel/staff';

interface StaffDetailProps {
  data?: IStaff | null;
}

const StaffDetail = ({ data }: StaffDetailProps) => {
  const id = data?.id;
  const {
    data: staff,
    isLoading,
    isError,
  } = useReadStaff({ id } as IReadStaff);

  if (isError) {
    return <div>Error fetching Contact</div>;
  }

  return (
    <div className="content">
      <div className="content-inner">
        <Box style={{ padding: '10px', margin: '10px' }}>
          <Typography variant="h5">Staff Details</Typography>
        </Box>

        {isLoading ? (
          <Box
            sx={{ display: 'flex', width: '100%', justifyContent: 'center' }}
          >
            <CircularProgress />
          </Box>
        ) : (
          <Card style={{ margin: '10px' }}>
            <TableContainer component={Paper}>
              <Table>
                <TableBody>
                  <TableRow>
                    <TableCell>
                      <strong>Name:</strong>
                    </TableCell>
                    <TableCell>{staff?.name}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <strong>Address:</strong>
                    </TableCell>
                    <TableCell>{staff?.address}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <strong>Phone (Residence):</strong>
                    </TableCell>
                    <TableCell>{staff?.rphone}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <strong>Phone (Mobile):</strong>
                    </TableCell>
                    <TableCell>{staff?.mphone}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <strong>Date of Joining:</strong>
                    </TableCell>
                    <TableCell>{staff?.jdate}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <strong>Position:</strong>
                    </TableCell>
                    <TableCell>{staff?.role}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>
                      <strong>Remark:</strong>
                    </TableCell>
                    <TableCell>{staff?.staff_remark}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Card>
        )}
      </div>
    </div>
  );
};

export default StaffDetail;
