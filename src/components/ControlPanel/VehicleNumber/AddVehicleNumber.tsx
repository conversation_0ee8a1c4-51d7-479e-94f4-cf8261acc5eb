import { useFormContext } from 'react-hook-form';

import {
  Autocomplete,
  Card,
  FormControl,
  Grid,
  TextField,
  Typography,
} from '@mui/material';

import { useReadDrivers } from '../../../hooks/controlpanel/drivers';

function AddVehicleNumber() {
  const { register, setValue } = useFormContext();
  const { data: drivers = [], isLoading } = useReadDrivers();

  return (
    <div className="content">
      <div className="content-inner">
        <Card style={{ marginTop: '20px', padding: '10px' }}>
          <Typography variant="h5" style={{ marginBottom: '20px' }}>
            Add New Vehicle Number
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                id="vehicle-no"
                type="number"
                variant="outlined"
                label="Vehicle Number"
                {...register('num')}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                id="more-info"
                variant="outlined"
                label="More Info"
                {...register('info')}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <FormControl fullWidth margin="normal">
                <Autocomplete
                  id="driver-select"
                  options={drivers}
                  loading={isLoading}
                  getOptionLabel={(option: any) => option?.name || ''}
                  onChange={(_, newValue) => {
                    setValue('driver_id', newValue?.id || null);
                    setValue('driver_name', newValue?.name || null);
                  }}
                  renderInput={params => (
                    <TextField
                      {...params}
                      label="Assign Driver"
                      variant="outlined"
                      helperText={isLoading ? 'Loading drivers...' : ''}
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </Grid>
        </Card>
      </div>
    </div>
  );
}

export default AddVehicleNumber;
