import { useEffect } from 'react';

import { useFormContext } from 'react-hook-form';

import {
  Autocomplete,
  Card,
  FormControl,
  Grid,
  TextField,
  Typography,
} from '@mui/material';

import { useReadDrivers } from '../../../hooks/controlpanel/drivers';

function EditVehicleNumber() {
  const { register, setValue, watch, getValues } = useFormContext();
  const { data: drivers = [] } = useReadDrivers();

  useEffect(() => {
    setValue('driver_id', getValues('driver_id'));
  }, [setValue, getValues]);

  return (
    <div className="content">
      <div className="content-inner">
        <Card style={{ marginTop: '20px', padding: '10px' }}>
          <Typography variant="h5" style={{ marginBottom: '20px' }}>
            Edit Vehicle Number
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                id="vehicle-no"
                type="number"
                variant="outlined"
                label="Vehicle Number"
                {...register('num')}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                id="more-info"
                variant="outlined"
                label="More Info"
                {...register('info')}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <FormControl fullWidth margin="normal">
                <Autocomplete
                  options={drivers}
                  getOptionLabel={option => option.name || ''}
                  onChange={(_, newValue) => {
                    setValue('driver_id', newValue?.id || null);
                  }}
                  value={
                    drivers.find(driver => driver.id === watch('driver_id')) ||
                    null
                  }
                  renderInput={params => (
                    <TextField
                      {...params}
                      label="Assign Driver"
                      variant="outlined"
                    />
                  )}
                />
              </FormControl>
            </Grid>
          </Grid>
        </Card>
      </div>
    </div>
  );
}

export default EditVehicleNumber;
