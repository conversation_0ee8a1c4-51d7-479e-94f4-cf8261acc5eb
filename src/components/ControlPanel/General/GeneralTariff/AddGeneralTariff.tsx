import React from 'react';

import { useFieldArray, useFormContext } from 'react-hook-form';

import {
  <PERSON><PERSON>,
  Card,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
} from '@mui/material';

import { useReadTransfers } from '../../../../hooks/controlpanel/transfer';
import { useReadVehicleTypes } from '../../../../hooks/controlpanel/vehicletype';

const AddGeneralTariff = () => {
  const { register, control } = useFormContext();
  const { data: transfers } = useReadTransfers();
  const { data: vtypes } = useReadVehicleTypes();

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'data',
  });

  return (
    <div className="content">
      <div className="content-inner">
        <Card style={{ marginTop: '20px', padding: '10px' }}>
          <Typography variant="h5" style={{ marginBottom: '20px' }}>
            Add General Tariff
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <FormControl fullWidth margin="normal" variant="outlined">
                <InputLabel id="vid-label">Vehicle Type</InputLabel>
                <Select label="Vehicle Type" {...register('vid')}>
                  {vtypes?.map(vtype => (
                    <MenuItem key={vtype.id} value={vtype.type}>
                      {vtype?.type}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            {fields.map((item, index) => (
              <React.Fragment key={item.id}>
                <Grid item xs={12} sm={5}>
                  <FormControl fullWidth margin="normal" variant="outlined">
                    <InputLabel id={`data[${index}].tid-label`}>
                      Transfer Type
                    </InputLabel>
                    <Select
                      labelId={`data[${index}].tid-label`}
                      id={`data[${index}].tid`}
                      label="Transfer Type"
                      {...register(`data[${index}].tid`)}
                    >
                      {transfers?.map(transfer => (
                        <MenuItem key={transfer.id} value={transfer.id}>
                          {transfer?.type}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={5}>
                  <TextField
                    id={`data[${index}].rate`}
                    variant="outlined"
                    label="Rate"
                    {...register(`data[${index}].rate`)}
                    fullWidth
                    margin="normal"
                    type="number"
                  />
                </Grid>
                <Grid item xs={12} sm={2}>
                  <Button
                    variant="contained"
                    color="secondary"
                    onClick={() => remove(index)}
                    style={{ marginTop: '20px' }}
                  >
                    Remove
                  </Button>
                </Grid>
              </React.Fragment>
            ))}
            <Grid item xs={12}>
              <Button
                variant="contained"
                color="primary"
                onClick={() => append({ tid: '', rate: '' })}
              >
                Add Rate
              </Button>
            </Grid>
          </Grid>
        </Card>
      </div>
    </div>
  );
};

export default AddGeneralTariff;
