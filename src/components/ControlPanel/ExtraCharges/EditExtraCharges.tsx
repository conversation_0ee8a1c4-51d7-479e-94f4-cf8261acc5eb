import { useFormContext } from 'react-hook-form';

import { Card, Grid, TextField, Typography } from '@mui/material';

function EditExtraCharges() {
  const { register } = useFormContext();

  return (
    <div className="content">
      <div className="content-inner">
        <Card style={{ marginTop: '20px', padding: '10px' }}>
          <Typography variant="h5" style={{ marginBottom: '20px' }}>
            Edit Extra Charges
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <TextField
                id="extra-type"
                variant="outlined"
                label="Type"
                {...register('type')}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <TextField
                id="code"
                variant="outlined"
                label="Code"
                {...register('code')}
                fullWidth
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <TextField
                id="rate"
                variant="outlined"
                label="Rate"
                {...register('rate')}
                fullWidth
                margin="normal"
                type="number"
              />
            </Grid>
          </Grid>
        </Card>
      </div>
    </div>
  );
}

export default EditExtraCharges;
