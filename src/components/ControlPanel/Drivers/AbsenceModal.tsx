import { useEffect, useState } from 'react';

import { FormProvider, useForm } from 'react-hook-form';

import CloseIcon from '@mui/icons-material/Close';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  TextField,
} from '@mui/material';

import SelectionDropdown from '@components/HireCharts/SelectionDropdown';

import {
  useReadDrivers,
  useRecordDriverAbsence,
} from '../../../hooks/controlpanel/drivers';

interface AbsenceModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  driverId?: number;
}

const AbsenceModal = ({
  open,
  onClose,
  onSuccess,
  driverId,
}: AbsenceModalProps) => {
  const [selectedDriver, setSelectedDriver] = useState<number | null>(
    driverId || null
  );
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [reason, setReason] = useState('');

  const methods = useForm({
    defaultValues: {
      driver: '',
    },
  });

  const { data: drivers, isLoading: isDriversLoading } = useReadDrivers();
  const recordAbsence = useRecordDriverAbsence();

  useEffect(() => {
    if (driverId) {
      setSelectedDriver(driverId);
    }
  }, [driverId]);

  const handleSubmit = () => {
    if (!selectedDriver || !startDate || !endDate) return;

    recordAbsence.mutate(
      {
        driverId: selectedDriver,
        startDate,
        endDate,
        reason,
      },
      {
        onSuccess: () => {
          onSuccess();
          onClose();
          setSelectedDriver(null);
          setStartDate('');
          setEndDate('');
          setReason('');
        },
      }
    );
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle
        sx={{
          m: 0,
          p: 2,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        Record Driver Absence
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            color: theme => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <FormProvider {...methods}>
        <DialogContent>
          <SelectionDropdown
            name="driver"
            options={drivers || []}
            label="Select Driver"
            renderLabel={(driver: any) => driver?.name}
            displayLabel="name"
            setSelectedValue={(data: any) => {
              setSelectedDriver(data?.id || null);
            }}
            loading={isDriversLoading}
            disabled={!!driverId}
          />
          <TextField
            label="Start Date"
            type="date"
            value={startDate}
            onChange={e => setStartDate(e.target.value)}
            fullWidth
            margin="normal"
            InputLabelProps={{
              shrink: true,
            }}
            required
          />
          <TextField
            label="End Date"
            type="date"
            value={endDate}
            onChange={e => setEndDate(e.target.value)}
            fullWidth
            margin="normal"
            InputLabelProps={{
              shrink: true,
            }}
            required
          />
          <TextField
            label="Reason"
            value={reason}
            onChange={e => setReason(e.target.value)}
            fullWidth
            margin="normal"
            multiline
            rows={4}
            required
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            color="primary"
            variant="contained"
            disabled={
              !selectedDriver ||
              !startDate ||
              !endDate ||
              !reason ||
              recordAbsence.isPending
            }
          >
            {recordAbsence.isPending ? 'Saving...' : 'Save'}
          </Button>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
};

export default AbsenceModal;
