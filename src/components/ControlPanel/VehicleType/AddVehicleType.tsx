import { useSnackbar } from 'notistack';
import { useFormContext } from 'react-hook-form';

import { Card, Grid, TextField, Typography } from '@mui/material';

import { useReadVehicleTypes } from '../../../hooks/controlpanel/vehicletype';

function AddVehicleType() {
  const { register, watch } = useFormContext();
  const { data: existingTypes = [] } = useReadVehicleTypes();
  const { enqueueSnackbar } = useSnackbar();

  // Watch the type field to validate as user types
  const currentType = watch('type');

  // Validate vehicle type as user types
  const validateVehicleType = (value: string) => {
    const typeExists = existingTypes.some(
      (vtype: any) => vtype.type.toLowerCase() === value.toLowerCase()
    );

    if (typeExists) {
      enqueueSnackbar('This vehicle type already exists', {
        variant: 'warning',
        preventDuplicate: true,
      });
    }
  };

  return (
    <div className="content">
      <div className="content-inner">
        <Card style={{ marginTop: '20px', padding: '10px' }}>
          <Typography variant="h5" style={{ marginBottom: '20px' }}>
            Add New Vehicle Type
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <TextField
                id="vehicle-type"
                variant="outlined"
                label="Vehicle Type"
                {...register('type', {
                  onChange: e => validateVehicleType(e.target.value),
                })}
                fullWidth
                margin="normal"
                error={existingTypes.some(
                  (vtype: any) =>
                    vtype.type.toLowerCase() === currentType?.toLowerCase()
                )}
                helperText={
                  existingTypes.some(
                    (vtype: any) =>
                      vtype.type.toLowerCase() === currentType?.toLowerCase()
                  )
                    ? 'This vehicle type already exists'
                    : ''
                }
              />
            </Grid>
            <Grid item xs={12} sm={6} md={4} lg={3}>
              <TextField
                id="more-info"
                variant="outlined"
                label="More Info"
                {...register('info')}
                fullWidth
                margin="normal"
              />
            </Grid>
          </Grid>
        </Card>
      </div>
    </div>
  );
}

export default AddVehicleType;
