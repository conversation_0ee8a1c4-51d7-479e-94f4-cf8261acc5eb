import { useFormContext } from 'react-hook-form';

import { Card, Grid, TextField, Typography } from '@mui/material';

function EditBillDates() {
  const { register } = useFormContext();

  return (
    <div className="content">
      <div className="content-inner">
        <Card style={{ marginTop: '20px', padding: '10px' }}>
          <Typography variant="h5" style={{ marginBottom: '20px' }}>
            Edit BillDates
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                id="bill-date"
                variant="outlined"
                label="Bill Date"
                {...register('billdate')}
                fullWidth
                margin="normal"
                type="date"
              />
            </Grid>
          </Grid>
        </Card>
      </div>
    </div>
  );
}

export default EditBillDates;
