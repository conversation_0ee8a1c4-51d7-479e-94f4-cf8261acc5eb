import { useEffect } from 'react';

import { getDefaultDateRange } from './dateFilters';

export const useMonthlyDateReset = (
  setDateRange: (range: { startDate: string; endDate: string }) => void,
  storageKey: string
) => {
  useEffect(() => {
    // Get saved date range or default if none exists
    const savedRange = localStorage.getItem(storageKey);
    const currentRange = savedRange
      ? JSON.parse(savedRange)
      : getDefaultDateRange();

    // Set initial date range from saved value
    setDateRange(currentRange);

    // Set up interval to check for month changes
    const interval = setInterval(() => {
      const defaultRange = getDefaultDateRange();
      // eslint-disable-next-line @typescript-eslint/no-shadow
      const savedRange = localStorage.getItem(storageKey);

      // Only update if there's no saved range or if it's a new month
      if (!savedRange) {
        setDateRange(defaultRange);
        localStorage.setItem(storageKey, JSON.stringify(defaultRange));
      }
    }, 1000 * 60); // Check every minute

    return () => clearInterval(interval);
  }, [setDateRange, storageKey]);
};
