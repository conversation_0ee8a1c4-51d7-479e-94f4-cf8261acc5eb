export const getFinancialYear = () => {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear().toString().slice(-2);
  const lastYear = (Number(currentYear) - 1).toString();
  const nextYear = (Number(currentYear) + 1).toString();

  const isNewFinancialYear = currentDate.getMonth() >= 3;

  return {
    startYear: isNewFinancialYear ? currentYear : lastYear,
    endYear: isNewFinancialYear ? nextYear : currentYear,
    getBillPrefix: () =>
      `JPN${isNewFinancialYear ? currentYear + nextYear : lastYear + currentYear}`,
  };
};
