import { ReactNode } from 'react';

import AssessmentOutlinedIcon from '@mui/icons-material/AssessmentOutlined';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import DashboardOutlinedIcon from '@mui/icons-material/DashboardOutlined';
import DescriptionOutlinedIcon from '@mui/icons-material/DescriptionOutlined';
import GroupOutlinedIcon from '@mui/icons-material/GroupOutlined';
import HistoryEduOutlinedIcon from '@mui/icons-material/HistoryEduOutlined';
import ReceiptLongIcon from '@mui/icons-material/ReceiptLong';
import SettingsOutlinedIcon from '@mui/icons-material/SettingsOutlined';

export interface IMenuItem {
  title: string;
  link?: string;
  onClick?: () => void;
  icon?: ReactNode;
  items?: IMenuItem[];
  roles?: number[]; // Array of user levels that can access this menu item
}

export const menu: IMenuItem[] = [
  {
    title: 'Home',
    link: '/',
    icon: <DashboardOutlinedIcon />,
    roles: [2, 3], // Admin and Editor
  },
  {
    title: 'Contacts',
    link: '/contacts',
    icon: <GroupOutlinedIcon />,
    roles: [2], // Admin only
  },
  {
    title: 'Hire Chart',
    icon: <CalendarMonthIcon />,
    roles: [2, 3], // Admin and Editor
    items: [
      {
        title: 'New Hire',
        link: '/hirechart',
        roles: [2, 3], // Admin and Editor
      },
      {
        title: 'Edit Hire',
        link: '/editHire',
        roles: [2, 3], // Admin and Editor
      },
      {
        title: 'PDC Hire',
        link: '/pdcHire',
        roles: [2, 3], // Admin and Editor
      },
    ],
  },
  {
    title: 'Bill',
    link: '/newBill',
    icon: <ReceiptLongIcon />,
    roles: [2], // Admin only
    items: [
      {
        title: 'Pending Bills',
        link: '/pendingBills',
        roles: [2], // Admin only
      },
      {
        title: 'Bill Register',
        link: '/billRegister',
        roles: [2], // Admin only
      },
      {
        title: 'New Bill',
        link: '/newBill',
        roles: [2], // Admin only
      },
    ],
  },
  {
    title: 'Proforma',
    link: '/proforma',
    icon: <DescriptionOutlinedIcon />,
    roles: [2], // Admin only
  },
  {
    title: 'Contracts',
    link: '/contracts',
    icon: <HistoryEduOutlinedIcon />,
    roles: [2], // Admin only
  },
  {
    title: 'Reports',
    link: '/unbilledHires',
    icon: <AssessmentOutlinedIcon />,
    roles: [2, 3], // Admin and Editor
    items: [
      {
        title: 'Unbilled Hires',
        link: '/unbilledHires',
        roles: [2, 3], // Admin and Editor
      },
      {
        title: 'Vehicle Suppliers',
        link: '/vehicleSuppliers',
        roles: [2], // Admin only
      },
      {
        title: 'Vehicle Numbers',
        link: '/VehicleNumbersReport',
        roles: [2], // Admin only
      },
      {
        title: 'Vehicle Drivers',
        link: '/VehicleDriversReport',
        roles: [2], // Admin only
      },
      {
        title: 'Breakdown Incidence',
        link: '/breakdownIncidence',
        roles: [2], // Admin only
      },
      {
        title: 'Driver Attendance Overview',
        link: '/all-drivers-overview-report',
        roles: [2], // Admin only
      },
      {
        title: 'Mark Driver Absent',
        link: '/driver',
        roles: [2], // Admin only
      },
    ],
  },
  {
    title: 'Control Panel',
    link: 'controlpanel',
    icon: <SettingsOutlinedIcon />,
    roles: [2], // Admin only
  },
];

// Utility function to filter menu items based on user role
export const getFilteredMenu = (userLevel: number): IMenuItem[] => {
  const filterMenuItems = (items: IMenuItem[]): IMenuItem[] =>
    items
      .filter(item => !item.roles || item.roles.includes(userLevel))
      .map(item => ({
        ...item,
        items: item.items ? filterMenuItems(item.items) : undefined,
      }))
      .filter(item => !item.items || item.items.length > 0);

  return filterMenuItems(menu);
};
