import { useEffect, useState } from 'react';

import { useNavigate } from 'react-router-dom';

import {
  Button,
  Grid,
  MenuItem,
  Paper,
  Select,
  TextField,
} from '@mui/material';

import PageTitle from '@components/PageTitle';

import { useReadCompanies } from '../../hooks/company';

const NewBill = () => {
  const [fromDate, setFromDate] = useState<string>('');
  // const [toDate, setToDate] = useState<string>('');
  const [company, setCompany] = useState<any>('');
  const [subAgentKeyword, setSubAgentKeyword] = useState<string>('');
  const [clientKeyword, setClientKeyword] = useState<string>('');
  // const [billTypes, setBillTypes] = useState<string[]>([]);

  const navigate = useNavigate();

  const { data: companiesData } = useReadCompanies();

  const [companies, setCompanies] = useState<any>([]);

  useEffect(() => {
    if (companiesData && companiesData.length) {
      setCompanies(companiesData);
      setCompany(companiesData[0].id);
    }
  }, [companiesData]);

  const makeNewBill = () => {
    const selectedCompany = companiesData?.find(
      c => String(c.id) === String(company)
    );
    localStorage.setItem('date', fromDate);
    localStorage.setItem('companyName', selectedCompany.name);
    localStorage.setItem('companyId', selectedCompany.id);
    localStorage.setItem('subagent', subAgentKeyword);
    localStorage.setItem('client', clientKeyword);
    navigate(`/newBill/edit`);
  };

  return (
    <Grid
      container
      direction="column"
      spacing={1}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '0.5rem', sm: '1rem' },
      }}
    >
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle text="New Bill" />
      </Grid>

      <Grid item xs container spacing={2}>
        <Paper elevation={2} sx={{ width: '100%' }}>
          <Grid container py={6} px={6} spacing={2} direction="column">
            <Grid item md={4}>
              <TextField
                label="Date"
                type="date"
                value={fromDate}
                onChange={e => setFromDate(e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            {/* </Grid>
              <Grid item xs={12} sm={6} md={4} lg={3}> */}
            {/* <Box> */}
            <Grid item md={4}>
              <Select
                labelId="company"
                id="company"
                value={company}
                onChange={e => setCompany(e.target.value)}
                label="Transfer"
              >
                (
                {companies?.map((t: any) => (
                  <MenuItem key={t.id} value={t.id}>
                    {t.name}
                  </MenuItem>
                ))}
                )
              </Select>
            </Grid>
            {/* </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={6} lg={3}> */}
            <Grid item md={4}>
              <TextField
                label="Sub Agent"
                value={subAgentKeyword}
                onChange={e => setSubAgentKeyword(e.target.value)}
              />
            </Grid>
            {/* </Grid>
              <Grid item xs={12} sm={6} md={6} lg={4}> */}
            <Grid item md={4}>
              <TextField
                label="Client"
                value={clientKeyword}
                onChange={e => setClientKeyword(e.target.value)}
              />
            </Grid>
            {/* </Grid>
              <Grid item xs={12} sm={2} md={2} lg={2}> */}
            <Grid item md={4}>
              <Button variant="contained" onClick={makeNewBill}>
                Okay
              </Button>
            </Grid>
          </Grid>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default NewBill;
