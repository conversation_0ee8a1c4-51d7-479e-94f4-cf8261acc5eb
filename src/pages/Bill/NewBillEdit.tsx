import { useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';
import { useNavigate, useParams } from 'react-router-dom';

import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import {
  Box,
  Button,
  Grid,
  MenuItem,
  Paper,
  Select, // Table,
  // TableBody,
  // TableCell,
  // TableContainer,
  // TableHead,
  // TableRow,
  TextField,
  Typography,
} from '@mui/material';

import MaterialTable from '@material-table/core';

import DeleteDialog from '@components/Dialog/DeleteDialog';
import PageTitle from '@components/PageTitle';

import { formatDate } from '../../helpers/dateFormat';
import {
  useAddBillReg,
  useAddDBillHire,
  useDeleteDBillHire,
  useReadDBillHires, // useReadBillReg,
  // useReadBills,
  useUpdateBillReg,
} from '../../hooks/bill';

// import { useReadExtras } from '../../hooks/extra';
// import { useReadHireCharts } from '../../hooks/hirechart';
// import { useReadPtolls } from '../../hooks/ptoll';

const NewBillDetail = () => {
  const { enqueueSnackbar } = useSnackbar();
  const { id } = useParams();

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const { data: bills } = useReadDBillHires();

  useEffect(() => {
    document.title = 'Bill - nunes';
    if (bills) {
      const dbBill = bills?.find(b => Number(b.billnum) === Number(id));
      if (dbBill) {
        setFromDate(dbBill.date);
        setVtype(dbBill.vtype);
        setVno(dbBill.vno);
        setParticular(dbBill.particulars);
        // setMintype(dbBill.minType)
        setKms(dbBill.kms);
        setExtraKms(dbBill.xkms);
        setRate(dbBill.rate);
        setTotal(dbBill.tot1);
        setExtraHrs(dbBill.xhrs);
        setWaitChgs(dbBill.wchr);
        setTotal2(dbBill.tot2);
        setEarly(dbBill.early);
        setEarlyMorning(dbBill.early_morning);
        setLate(dbBill.late);
        setOvernight(dbBill.onite);
        setExtras(dbBill.toll);
        setTotal3(dbBill.tot3);
        setBillNo(dbBill.billnum);
      }
    }
    // eslint-disable-next-line
  }, [bills]);

  const [currentRow, setCurrentRow] = useState<any>(null);
  const [premark, setPremark] = useState('');
  const [paid, setPaid] = useState('0');
  const [date] = useState(localStorage.getItem('date'));
  const [company] = useState(localStorage.getItem('companyName'));
  const [billNo, setBillNo] = useState('');

  const [allTotal, setAllTotal] = useState(0.0);
  const [serviceTax, setServiceTax] = useState(0.0);
  const [eduCess, setEduCess] = useState(0.0);
  const [grandTotal, setGrandTotal] = useState(0.0);

  const [fromDate, setFromDate] = useState('');
  const [vtype, setVtype] = useState('');
  const [vno, setVno] = useState('');
  const [particular, setParticular] = useState('');
  const [minType] = useState('');
  const [kms, setKms] = useState('');
  const [rate, setRate] = useState('');
  const [extraKms, setExtraKms] = useState('');
  const [total, setTotal] = useState('');
  const [extraHrs, setExtraHrs] = useState('');
  const [waitChgs, setWaitChgs] = useState('');
  const [total2, setTotal2] = useState('');
  const [early, setEarly] = useState('');
  const [earlyMorning, setEarlyMorning] = useState('');
  const [late, setLate] = useState('');
  const [overnight, setOvernight] = useState('');
  const [extras, setExtras] = useState('');
  const [total3, setTotal3] = useState('');

  const { mutate: addBillRegMutate } = useAddBillReg();
  const { mutate: useAddDBillHireMutate } = useAddDBillHire();
  const { mutate: updateBillRegMutate } = useUpdateBillReg();
  const { mutate: useDeleteDBillHireMutate } = useDeleteDBillHire();

  const navigate = useNavigate();

  const [hirecharts, setHirecharts] = useState<any>([]);

  const handleEdit = (rowData: any) => {
    if (rowData) {
      navigate(`/newBill/edit/${billNo}/hire`);
    }
  };

  const handleDelete = (rowData: any) => {
    setCurrentRow(rowData);
    setDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  const updateBillDetails = () => {
    if (billNo && billNo !== '') {
      updateBillRegMutate(
        { premark, paid, id: billNo },
        {
          onSuccess: () => {
            enqueueSnackbar('Content Updated', { variant: 'info' });
          },
          onError: () => {
            enqueueSnackbar('Failed to Update Content', { variant: 'error' });
          },
        }
      );
    }
  };

  const addBillDetails = () => {
    addBillRegMutate(
      { date: fromDate, type: '3' },
      {
        onSuccess: (sdata: any) => {
          setBillNo(sdata!.data?.id);

          // eslint-disable-next-line
          useAddDBillHireMutate(
            {
              date: fromDate,
              vtype,
              vno,
              particulars: particular,
              minType,
              initial: kms,
              xkm: extraKms,
              rate,
              tot1: total,
              xhr: extraHrs,
              wch: waitChgs,
              tot2: total3,
              early,
              early_morning: earlyMorning,
              late,
              onite: overnight,
              extra: extras,
              total: total3,
              billnum: sdata!.data?.id,
            },
            {
              onSuccess: () => {
                enqueueSnackbar('Content Updated', { variant: 'info' });
              },
              onError: () => {
                enqueueSnackbar('Failed to Update Content', {
                  variant: 'error',
                });
              },
            }
          );
        },
        onError: () => {
          enqueueSnackbar('Failed to Update Content', { variant: 'error' });
        },
      }
    );
    const newData = [
      {
        date: fromDate,
        vtype,
        vno,
        particulars: particular,
        minType,
        kms,
        xkms: extraKms,
        rate,
        tot1: total,
        xhrs: extraHrs,
        wchr: waitChgs,
        tot2: total3,
        early,
        early_morning: earlyMorning,
        late,
        onite: overnight,
        toll: extras,
        tot3: total3,
      },
    ];
    setAllTotal(Number(allTotal) + Number(total3));
    setServiceTax(((Number(allTotal) + Number(total3)) * 4.8) / 100);
    setEduCess(((Number(allTotal) + Number(total3)) * 4.8) / 100);
    setGrandTotal(
      Number(allTotal) +
        Number(total3) +
        ((Number(allTotal) + Number(total3)) * 4.8) / 100 +
        ((Number(allTotal) + Number(total3)) * 4.8) / 100
    );
    setHirecharts(newData);
  };

  const handleConfirmDelete = () => {
    if (currentRow) {
      // eslint-disable-next-line
      useDeleteDBillHireMutate(
        { id: +currentRow.id },
        {
          onSuccess: () => {
            setHirecharts((prevContracts: any) =>
              prevContracts.filter((c: any) => c.id !== currentRow.id)
            );
            enqueueSnackbar('Content Deleted', { variant: 'success' });
          },
          onError: () => {
            enqueueSnackbar('Failed to Delete Content', { variant: 'error' });
          },
        }
      );
    }
    setDeleteDialogOpen(false);
  };

  const columns: any = [
    {
      title: 'Date',
      field: 'date',
      defaultSort: 'desc',
      render: (rowData: any) => `${formatDate(rowData?.hirechart?.date)} `,
    },
    {
      title: 'V type',
      field: 'vtype',
      render: (rowData: any) => `${rowData?.hirechart?.vtype} `,
    },
    {
      title: 'Vehicle No',
      field: 'vehicleno',
      render: (rowData: any) => `${rowData?.hirechart?.vno || ''} `,
    },
    {
      title: 'Particular',
      render: (rowData: any) =>
        rowData?.hirechart?.particulars_type
          ? `${rowData?.hirechart?.particulars} ${rowData?.hirechart?.particulars_type}`
          : rowData?.hirechart?.particulars || '',
    },
    {
      title: '8 Hrs / 100 KMs',
      field: 'kms',
      render: (rowData: any) => `${rowData?.bill?.kms || 0} `,
    },
    {
      title: 'Ex Kms',
      field: 'xkms',
      render: (rowData: any) => `${rowData?.bill?.xkms || 0} `,
    },
    {
      title: 'Rate',
      field: 'rate',
      render: (rowData: any) => `${rowData?.bill?.rate || 0} `,
    },
    {
      title: 'Tot',
      field: 'tot1',
      render: (rowData: any) => `${rowData?.bill?.tot1 || 0} `,
    },
    {
      title: 'Ex HRs',
      field: 'xhrs',
      render: (rowData: any) => `${rowData?.bill?.xhrs || 0} `,
    },
    {
      title: 'Wait Ch',
      field: 'wchr',
      render: (rowData: any) => `${rowData?.bill?.wchr || 0} `,
    },
    {
      title: 'Tot',
      field: 'tot2',
      render: (rowData: any) => `${rowData?.bill?.tot2 || 0} `,
    },
    {
      title: 'Early',
      field: 'early',
      render: (rowData: any) => `${rowData?.bill?.early || 0} `,
    },
    {
      title: 'Early Morning',
      field: 'early_morning',
      render: (rowData: any) => `${rowData?.bill?.early_morning || 0} `,
    },
    {
      title: 'Late',
      field: 'late',
      render: (rowData: any) => `${rowData?.bill?.late || 0} `,
    },
    {
      title: 'Nite',
      field: 'onite',
      render: (rowData: any) => `${rowData?.bill?.onite || 0} `,
    },
    {
      title: 'Extra',
      field: 'toll',
      render: (rowData: any) => `${rowData?.bill?.toll || 0} `,
    },
    {
      title: 'Total',
      field: 'tot3',
      render: (rowData: any) => `${rowData?.bill?.tot3 || 0} `,
    },
  ];

  return (
    <Grid
      container
      direction="column"
      spacing={1}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '0.5rem', sm: '1rem' },
      }}
    >
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle text="New sasBill" />
      </Grid>

      <Paper elevation={2} sx={{ width: '100%' }}>
        <Grid
          container
          item
          direction="row"
          justifyContent="space-between"
          paddingTop={2}
          paddingBottom={4}
        >
          <Grid item paddingLeft={2}>
            <Typography variant="h6">Date: {formatDate(date)}</Typography>
            <Typography variant="h6">Bill No: JPN/{billNo}</Typography>
            <Typography variant="h6">Company: {company}</Typography>
          </Grid>
          <Grid item paddingRight={8} marginRight={6}>
            <Grid item paddingBottom={2}>
              <Select
                labelId="paid"
                id="paid"
                value={paid}
                onChange={e => setPaid(e.target.value)}
                label="Paid"
              >
                <MenuItem value="0">Unpaid</MenuItem>
                <MenuItem value="1">Paid</MenuItem>
              </Select>
            </Grid>
            <Grid item paddingBottom={2}>
              <TextField
                id="premark"
                value={premark}
                onChange={e => setPremark(e.target.value)}
                label="Remarks"
                variant="outlined"
                multiline
                rows={3}
              />
            </Grid>
            <Grid>
              <Button variant="contained" onClick={updateBillDetails}>
                Update
              </Button>
            </Grid>
          </Grid>
        </Grid>

        <Grid item xs container spacing={2}>
          {/* <Paper elevation={2} sx={{ width: '100%' }}> */}
          <Grid item xs={12}>
            <MaterialTable
              title=""
              columns={columns}
              data={hirecharts || []}
              options={{
                draggable: false,
                paging: false,
                search: true,
                tableLayout: 'fixed',
                searchFieldVariant: 'outlined',
              }}
              style={{
                minHeight: 'calc(100vh - 180px)',
                overflowY: 'auto',
              }}
              components={{
                Container: props => <Paper elevation={0} {...props} />,
              }}
              actions={[
                {
                  icon: EditIcon,
                  tooltip: 'Edit Bill',
                  onClick: (_, rowData) => handleEdit(rowData),
                },
                {
                  icon: DeleteIcon,
                  tooltip: 'Delete Bill',
                  onClick: (_, rowData) => handleDelete(rowData),
                },
              ]}
            />
            <Grid
              item
              container
              direction="column"
              justifyContent="flex-end"
              alignItems="flex-end"
              paddingRight={4}
            >
              <Grid item>
                <Typography>Total: {allTotal}</Typography>
              </Grid>
              <Grid item>
                <Typography>Service Tax: {serviceTax} </Typography>
              </Grid>
              <Grid item>
                <Typography>Edu Cess: {eduCess}</Typography>
              </Grid>
              <Grid item>
                <Typography>G. Total: {grandTotal}</Typography>
              </Grid>
            </Grid>
          </Grid>
          {/* </Paper> */}
        </Grid>

        <div id="printData">
          <Box sx={{ p: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={12}>
                <Typography variant="h4">Add Hire</Typography>
              </Grid>
              <Grid item xs={12} md={12}>
                <Box>
                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <Box>
                      <TextField
                        label="Date"
                        type="date"
                        value={fromDate}
                        onChange={e => setFromDate(e.target.value)}
                        InputLabelProps={{
                          shrink: true,
                        }}
                        fullWidth
                      />
                    </Box>
                  </Box>
                  <Box my={4} gap={4}>
                    <Box>
                      <TextField
                        id="vtype"
                        value={vtype}
                        onChange={e => setVtype(e.target.value)}
                        label="Veh Type"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                  <Box my={4} gap={4}>
                    <Box>
                      <TextField
                        id="vno"
                        value={vno}
                        onChange={e => setVno(e.target.value)}
                        label="Veh No"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                  <Box my={4} gap={4}>
                    <Box>
                      <TextField
                        id="particular"
                        value={particular}
                        onChange={e => setParticular(e.target.value)}
                        label="Particulars"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <Box marginRight={3} paddingRight={4}>
                      <Typography>8 HRs / 100 KMs</Typography>
                    </Box>
                    <Box marginLeft={3} paddingLeft={4}>
                      <TextField
                        id="kms"
                        value={kms}
                        onChange={e => setKms(e.target.value)}
                        label="Kms"
                        variant="outlined"
                      />
                    </Box>
                  </Box>

                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <Box>
                      <TextField
                        id="extraKms"
                        value={extraKms}
                        onChange={e => setExtraKms(e.target.value)}
                        label="Extra Kms"
                        variant="outlined"
                      />
                    </Box>
                    <Box>
                      <TextField
                        id="rate"
                        value={rate}
                        onChange={e => setRate(e.target.value)}
                        label="Rate"
                        variant="outlined"
                      />
                    </Box>

                    <Box>
                      <TextField
                        id="total"
                        value={total}
                        onChange={e => setTotal(e.target.value)}
                        label="Total"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <Box>
                      <TextField
                        id="extraHrs"
                        value={extraHrs}
                        onChange={e => setExtraHrs(e.target.value)}
                        label="Extra Hours"
                        variant="outlined"
                      />
                    </Box>
                    <Box>
                      <TextField
                        id="waitChgs"
                        value={waitChgs}
                        onChange={e => setWaitChgs(e.target.value)}
                        label="Wait Charges"
                        variant="outlined"
                      />
                    </Box>
                    <Box>
                      <TextField
                        id="total2"
                        value={total2}
                        onChange={e => setTotal2(e.target.value)}
                        label="Total"
                        variant="outlined"
                      />
                    </Box>
                  </Box>

                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <Box>
                      <TextField
                        id="early"
                        value={early}
                        onChange={e => setEarly(e.target.value)}
                        label="Early"
                        variant="outlined"
                      />
                    </Box>
                    <Box>
                      <TextField
                        id="earlyMorning"
                        value={earlyMorning}
                        onChange={e => setEarlyMorning(e.target.value)}
                        label="Early Morning"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <Box>
                      <TextField
                        id="late"
                        value={late}
                        onChange={e => setLate(e.target.value)}
                        label="Late"
                        variant="outlined"
                      />
                    </Box>
                    <Box>
                      <TextField
                        id="overnight"
                        value={overnight}
                        onChange={e => setOvernight(e.target.value)}
                        label="Overnight"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <TextField
                      id="extras"
                      value={extras}
                      onChange={e => setExtras(e.target.value)}
                      label="Extras"
                      variant="outlined"
                    />
                  </Box>
                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <TextField
                      id="total3"
                      value={total3}
                      onChange={e => setTotal3(e.target.value)}
                      label="Total"
                      variant="outlined"
                    />
                  </Box>
                  <Box>
                    <Button variant="contained" onClick={addBillDetails}>
                      Update
                    </Button>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </div>
      </Paper>

      <DeleteDialog
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        handleConfirmDelete={handleConfirmDelete}
      />
    </Grid>
  );
};

export default NewBillDetail;
