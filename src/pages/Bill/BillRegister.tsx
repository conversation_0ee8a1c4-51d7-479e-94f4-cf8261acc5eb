import { useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';
import { Controller, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Button,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
} from '@mui/material';

import MaterialTable, { Column } from '@material-table/core';

import DeleteDialog from '@components/Dialog/DeleteDialog';
import PageTitle from '@components/PageTitle';

import { getDefaultDateRange } from '../../helpers/dateFilters';
import { formatDate } from '../../helpers/dateFormat';
import { useMonthlyDateReset } from '../../helpers/useMonthlyDateReset';
import { useDeleteBillReg, useGetBillRegFiltered } from '../../hooks/bill';
import { useReadCompanies } from '../../hooks/company';
import { useReadContracts } from '../../hooks/contract';
import { useReadHireCharts } from '../../hooks/hirechart';

const BillRegister = () => {
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const [search, setSearch] = useState(false);
  const { data: companiesData } = useReadCompanies();
  const [companies, setCompanies] = useState<any>([]);
  const { data: hirecharts, isLoading: isLoadingHires } = useReadHireCharts();
  const { data: contracts } = useReadContracts();
  const { mutate: deleteBillRegMutate } = useDeleteBillReg();

  const [selectedBill, setSelectedBill] = useState<any>('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [filteredBills, setFilteredBills] = useState<any[]>([]);

  const savedDateRange = (() => {
    const savedData = localStorage.getItem('BillRegisterFilteredDateRange');
    return savedData ? JSON.parse(savedData) : getDefaultDateRange();
  })();

  const [dateRange, setDateRange] = useState(savedDateRange);

  const { handleSubmit, control, watch, reset } = useForm({
    mode: 'onChange',
    defaultValues: {
      startDate: savedDateRange.startDate,
      endDate: savedDateRange.endDate,
      company: '',
    },
  });

  useEffect(() => {
    reset({
      ...watch(),
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    });
  }, [dateRange, reset, watch]);

  const watchedStartDate = watch('startDate');
  const watchedEndDate = watch('endDate');

  useEffect(() => {
    localStorage.setItem(
      'BillRegisterFilteredDateRange',
      JSON.stringify({
        startDate: watchedStartDate,
        endDate: watchedEndDate,
      })
    );
  }, [watchedStartDate, watchedEndDate]);

  useMonthlyDateReset(setDateRange, 'BillRegisterFilteredDateRange');

  const { data, isLoading, refetch } = useGetBillRegFiltered(
    {
      startDate: watchedStartDate,
      endDate: watchedEndDate,
      company: watch('company'),
    },
    search
  ) as any;

  useEffect(() => {
    document.title = 'Bill - nunes';
  }, []);

  useEffect(() => {
    if (companiesData && companiesData.length) {
      setCompanies(companiesData);
    }
  }, [companiesData]);

  const handleDetails = (rowData: any) => {
    if (rowData) {
      setSelectedBill(rowData);
      if (rowData.type === '1') navigate(`/billRegister/${rowData.id}`);
      if (rowData.type === '2') {
        const selectedContract = contracts?.filter(
          c => c.billno === rowData.id
        );
        if (selectedContract && selectedContract.length)
          navigate(`/contractDetails/${selectedContract[0].id}`);
      }
    }
  };

  const handleEdit = (rowData: any) => {
    setSelectedBill(rowData);
    if (rowData.type === '1') navigate(`/billRegister/${rowData.id}`);
    if (rowData.type === '2') {
      const selectedContract = contracts?.filter(c => c.billno === rowData.id);
      if (selectedContract && selectedContract.length)
        navigate(`/contractDetails/${selectedContract[0].id}`);
    }
  };

  const handleDelete = (rowData: any) => {
    setSelectedBill(rowData);
    setDeleteDialogOpen(true);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  const handleConfirmDelete = () => {
    if (selectedBill) {
      deleteBillRegMutate(
        { id: selectedBill?.id },
        {
          onSuccess: () => {
            enqueueSnackbar('Content Deleted', { variant: 'success' });
            refetch();
          },
          onError: () => {
            enqueueSnackbar('Failed to Update Content', { variant: 'error' });
          },
        }
      );
    }
    setDeleteDialogOpen(false);
  };

  useEffect(() => {
    if (data) {
      const processedData = data
        ?.filter((d: any) => d.status === '1')
        .map((br: any, index: number) => {
          const hc: any = hirecharts?.find(h => h.billno === br.id);
          return {
            ...br,
            company: hc?.company || '',
            sr: index + 1,
          };
        });

      const currentCompany = watch('company');

      const filteredData = currentCompany
        ? processedData.filter((br: any) => br.company === currentCompany)
        : processedData;

      setFilteredBills(filteredData || []);
    }
  }, [data, hirecharts, watch]);

  const handleSearch = handleSubmit(() => {
    setSearch(!search);
  });

  const columns: Column<any>[] = [
    {
      title: 'S.No',
      field: 'sr',
      sorting: true,
      width: '5%',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        color: '#000000',
        fontSize: '0.75rem',
        fontWeight: 500,
        whiteSpace: 'nowrap',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
      },
      cellStyle: {
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        padding: '8px',
        fontSize: '0.75rem',
      },
    },
    {
      title: 'Date',
      field: 'date',
      render: rowData => formatDate(rowData.date),
      headerStyle: {
        backgroundColor: '#f5f5f5',
        color: '#000000',
        fontSize: '0.75rem',
        fontWeight: 500,
        whiteSpace: 'nowrap',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
      },
      cellStyle: {
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        padding: '8px',
        fontSize: '0.75rem',
      },
    },
    {
      title: 'Bill No',
      field: 'billnum',
      defaultSort: 'desc',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        color: '#000000',
        fontSize: '0.75rem',
        fontWeight: 500,
        whiteSpace: 'nowrap',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
      },
      cellStyle: {
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        padding: '8px',
        fontSize: '0.75rem',
      },
    },
    {
      title: 'Type',
      field: 'type',
      render: rowData => (rowData.type === '1' ? 'Hire' : 'Contract'),
      headerStyle: {
        backgroundColor: '#f5f5f5',
        color: '#000000',
        fontSize: '0.75rem',
        fontWeight: 500,
        whiteSpace: 'nowrap',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
      },
      cellStyle: {
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        padding: '8px',
        fontSize: '0.75rem',
      },
    },
    {
      title: 'Company',
      field: 'company',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        color: '#000000',
        fontSize: '0.75rem',
        fontWeight: 500,
        whiteSpace: 'nowrap',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
      },
      cellStyle: {
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        padding: '8px',
        fontSize: '0.75rem',
      },
    },
    {
      title: 'Total',
      field: 'total',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        color: '#000000',
        fontSize: '0.75rem',
        fontWeight: 500,
        whiteSpace: 'nowrap',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
      },
      cellStyle: {
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        padding: '8px',
        fontSize: '0.75rem',
      },
    },
    {
      title: 'Payment',
      field: 'paid',
      render: rowData => (rowData.paid === '0' ? 'Unpaid' : 'Paid'),
      headerStyle: {
        backgroundColor: '#f5f5f5',
        color: '#000000',
        fontSize: '0.75rem',
        fontWeight: 500,
        whiteSpace: 'nowrap',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
      },
      cellStyle: {
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        padding: '8px',
        fontSize: '0.75rem',
      },
    },
    {
      title: 'Remarks',
      field: 'premark',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        color: '#000000',
        fontSize: '0.75rem',
        fontWeight: 500,
        whiteSpace: 'nowrap',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
      },
      cellStyle: {
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        padding: '8px',
        fontSize: '0.75rem',
      },
    },
  ];

  return (
    <Grid
      container
      direction="column"
      spacing={1}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '0.5rem', sm: '1rem' },
      }}
    >
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle text="Register Bills" />
      </Grid>

      <Grid item xs container spacing={2}>
        <Paper elevation={2} sx={{ width: '100%' }}>
          <Accordion defaultExpanded>
            <AccordionSummary
              expandIcon={
                <ExpandMoreIcon
                  sx={{
                    color: 'primary.main',
                    '&.Mui-expanded': { transform: 'rotate(180deg)' },
                  }}
                />
              }
              aria-controls="filter-content"
              id="filter-header"
              sx={{
                '& .MuiAccordionSummary-content': {
                  alignItems: 'center',
                  fontSize: '1.2rem',
                  fontWeight: 'bold',
                  color: 'primary.main',
                },
              }}
            >
              <Typography variant="h6">Filters</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <form onSubmit={handleSearch}>
                <Grid container spacing={2} style={{ padding: '10px' }}>
                  <Grid item xs={6} md={3}>
                    <Controller
                      control={control}
                      name="startDate"
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Start Date"
                          type="date"
                          variant="outlined"
                          fullWidth
                          InputLabelProps={{ shrink: true }}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Controller
                      control={control}
                      name="endDate"
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="End Date"
                          type="date"
                          variant="outlined"
                          fullWidth
                          InputLabelProps={{ shrink: true }}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <FormControl fullWidth variant="outlined">
                      <InputLabel htmlFor="company">Company</InputLabel>
                      <Controller
                        control={control}
                        name="company"
                        render={({ field }) => (
                          <Select
                            {...field}
                            label="Company"
                            inputProps={{
                              id: 'company',
                            }}
                          >
                            <MenuItem value="">
                              <em>Select Company</em>
                            </MenuItem>
                            {companies?.map((comp: any) => (
                              <MenuItem key={comp?.id} value={comp?.name}>
                                {comp?.name}
                              </MenuItem>
                            ))}
                          </Select>
                        )}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleSearch}
                    >
                      Search
                    </Button>
                  </Grid>
                </Grid>
              </form>
            </AccordionDetails>
          </Accordion>

          <Grid item xs={12}>
            <MaterialTable
              title=""
              columns={columns}
              data={filteredBills || []}
              isLoading={isLoading && isLoadingHires}
              options={{
                draggable: false,
                paging: true,
                pageSize: 20,
                pageSizeOptions: [10, 20, 30],
                search: false,
                searchFieldAlignment: 'right',
                searchFieldStyle: {
                  marginLeft: 'auto',
                  marginRight: '2rem',
                },
                headerStyle: {
                  backgroundColor: '#f5f5f5',
                  color: '#000000',
                  fontSize: '0.75rem',
                  fontWeight: 500,
                },
                tableLayout: 'fixed',
                actionsColumnIndex: -1,
              }}
              style={{
                minHeight: 'calc(100vh - 180px)',
                overflowY: 'auto',
              }}
              components={{
                Container: props => <Paper elevation={0} {...props} />,
                Toolbar: () => <div style={{ margin: '10px' }}> </div>,
              }}
              onRowClick={(_, rowData) => handleDetails(rowData)}
              actions={[
                {
                  icon: EditIcon,
                  tooltip: 'Edit Bill',
                  onClick: (_, rowData) => handleEdit(rowData),
                },
                {
                  icon: DeleteIcon,
                  tooltip: 'Delete Bill',
                  onClick: (_, rowData) => handleDelete(rowData),
                },
              ]}
            />
          </Grid>
        </Paper>
      </Grid>

      <DeleteDialog
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        handleConfirmDelete={handleConfirmDelete}
      />
    </Grid>
  );
};

export default BillRegister;
