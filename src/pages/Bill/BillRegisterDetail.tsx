import { useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';
import { useNavigate, useParams } from 'react-router-dom';

import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import {
  Box,
  Button,
  Grid,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
} from '@mui/material';

import MaterialTable from '@material-table/core';

import DeleteDialog from '@components/Dialog/DeleteDialog';
import PageTitle from '@components/PageTitle';

import { formatDate } from '../../helpers/dateFormat';
import { useAddAdditional } from '../../hooks/additional';
import {
  useReadBillReg,
  useReadBills,
  useUpdateBill,
  useUpdateBillReg,
  useUpdateBillRegByBillNo,
} from '../../hooks/bill';
import { useReadCompanies } from '../../hooks/company';
// import { useReadExtras } from '../../hooks/extra';
import { useReadHireCharts, useUpdateHireChart } from '../../hooks/hirechart';

// import { useReadPtolls } from '../../hooks/ptoll';

const BillRegisterDetail = () => {
  const { enqueueSnackbar } = useSnackbar();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const [selectedBill, setSelectedBill] = useState<any>(null);

  const { mutate: updateHirechartMutate } = useUpdateHireChart();
  const { mutate: updateBillMutate } = useUpdateBill();
  const { mutate: updateBillRegByBillNoMutate } = useUpdateBillRegByBillNo();
  const { mutate: updateBillRegMutate } = useUpdateBillReg();
  const { mutate: addAdditionalMutate } = useAddAdditional();

  const [premark, setPremark] = useState('');
  const [paid, setPaid] = useState('0');

  const [discountAmount, setDiscountAmount] = useState('0');
  const [additionalAmount, setAdditionalAmount] = useState('0');
  const [additionalParticular, setAdditionalParticular] = useState('');

  const [total, setTotal] = useState(0);
  const [discount, setDiscount] = useState(0);
  const [netTaxable, setNetTaxable] = useState(0);
  const [gstAmount, setGstAmount] = useState(0);
  const [gstValue, setGstValue] = useState(0);
  const [grandTotal, setGrandTotal] = useState(0);

  const { id } = useParams();
  const { data: billList } = useReadBills();

  const [hirechartList, setHirechartList] = useState<any>([]);

  const [details, setDetails] = useState({
    company: '',
    billnum: '',
    cname: '',
    address: '',
    gstno: '',
    panno: '',
    subagent: '',
  });

  const { data: billReg } = useReadBillReg({ id });

  const { data: companies } = useReadCompanies();

  // eslint-disable-next-line
  let { data: hirecharts, isLoading, refetch } = useReadHireCharts() as any;

  useEffect(() => {
    document.title = 'Bill - nunes';
    if (billReg) {
      setPremark(billReg.premark);
      setPaid(billReg.paid);
    }
  }, [billReg]);

  useEffect(() => {
    if (hirecharts && billList && companies) {
      // eslint-disable-next-line
      const currentHirecharts = hirecharts?.filter(
        (h: any) => Number(h.billno) === Number(id)
      );

      // eslint-disable-next-line
      const mappedHirecharts = currentHirecharts.map((h: any) => {
        const billData = billList.find(
          (b: any) => Number(b.hid) === Number(h.id)
        );

        const bill = {
          kms: Number(billData?.kms || 0),
          xkms: Number(billData?.xkms || 0),
          rate: Number(billData?.rate || 0),
          tot1: Number(billData?.tot1 || 0),
          xhrs: Number(billData?.xhrs || 0),
          wchr: Number(billData?.wchr || 0),
          tot2: Number(billData?.tot2 || 0),
          early: Number(billData?.early || 0),
          early_morning: Number(billData?.early_morning || 0),
          late: Number(billData?.late || 0),
          onite: Number(billData?.onite || 0),
          toll: Number(billData?.toll || 0),
          tot3: Number(billData?.tot3 || 0),
        };

        return {
          bill,
          hirechart: h || {},
          hirechartData: h || {},
          billData: billData || {},
        };
      });

      if (mappedHirecharts.length > 0) {
        // Find company data
        const companyData = companies.find(
          (c: any) => c.name === mappedHirecharts[0].hirechartData.company
        );

        if (companyData) {
          setDetails({
            company: companyData.name || '',
            billnum: companyData.billnum || '',
            cname: companyData.cname || '',
            address: companyData.address || '',
            gstno: companyData.gstno || '',
            panno: companyData.panno || '',
            subagent: companyData.subagent || '',
          });

          setGstValue(Number(companyData.gst || 0));
        }

        const billTotal = mappedHirecharts.reduce(
          (accumulator: number, h: any) => {
            const tot3 = Number(h.bill.tot3) || 0;
            return accumulator + tot3;
          },
          0
        );

        setTotal(billTotal);
        setHirechartList(mappedHirecharts);
        setDiscount(Number(mappedHirecharts[0]?.billData?.discount || 0));
      }
    }
  }, [hirecharts, billList, companies, id]);

  useEffect(() => {
    const netTaxableAmount = total - discount;
    setNetTaxable(netTaxableAmount);
    const gstAmt = netTaxableAmount * (gstValue / 100);
    setGstAmount(gstAmt);
    setGrandTotal(netTaxableAmount + gstAmt);
  }, [total, discount, gstValue]);

  const navigate = useNavigate();

  const handleEdit = (rowData: any) => {
    if (rowData) {
      navigate(`/billRegister/${rowData.billData.id}/edit`);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  const handleDelete = (rowData: any) => {
    setSelectedBill(rowData);
    setDeleteDialogOpen(true);
  };

  const updateContractDetails = () => {
    updateBillRegMutate(
      { premark, paid, id },
      {
        onSuccess: () => {
          enqueueSnackbar('Content Updated', { variant: 'info' });
        },
        onError: () => {
          enqueueSnackbar('Failed to Update Content', { variant: 'error' });
        },
      }
    );
  };

  const updateAdditionalDetails = () => {
    addAdditionalMutate(
      {
        billNo: id,
        particular: additionalParticular,
        amount: additionalAmount,
      },
      {
        onSuccess: () => {
          enqueueSnackbar('Content Updated', { variant: 'info' });
        },
        onError: () => {
          enqueueSnackbar('Failed to Update Content', { variant: 'error' });
        },
      }
    );
  };

  const updateDiscountDetails = () => {
    updateBillRegByBillNoMutate(
      { billNum: id, discount: discountAmount },
      {
        onSuccess: () => {
          enqueueSnackbar('Content Updated', { variant: 'info' });
        },
        onError: () => {
          enqueueSnackbar('Failed to Update Content', { variant: 'error' });
        },
      }
    );
  };

  const handleConfirmDelete = () => {
    if (selectedBill) {
      updateHirechartMutate(
        { id: +selectedBill.hirechartData.id, billno: '0' },
        {
          onSuccess: () => {
            updateBillMutate(
              { id: +selectedBill.billData.id, billed: 0 },
              {
                onSuccess: () => {
                  updateBillRegMutate(
                    {
                      id: +selectedBill.hirechartData.billno,
                      total:
                        Number(grandTotal) -
                        Number(selectedBill?.billData?.tot3 || 0),
                    },
                    {
                      onSuccess: () => {
                        setGrandTotal(
                          Number(grandTotal) -
                            Number(selectedBill?.billData?.tot3 || 0)
                        );
                        enqueueSnackbar('Content Deleted', {
                          variant: 'success',
                        });
                        refetch();
                      },
                    }
                  );
                },
              }
            );
          },
          onError: () => {
            enqueueSnackbar('Failed to Delete Content', { variant: 'error' });
          },
        }
      );
    }
    setDeleteDialogOpen(false);
  };

  const columns: any = [
    {
      title: 'Date',
      field: 'date',
      defaultSort: 'desc',
      render: (rowData: any) => `${formatDate(rowData?.hirechart?.date)} `,
    },
    {
      title: 'Vehicle Type',
      field: 'vtype',
      render: (rowData: any) => `${rowData?.hirechart?.vtype} `,
    },
    {
      title: 'Vehicle No',
      field: 'vehicleno',
      render: (rowData: any) => `${rowData?.hirechart?.vno || ''} `,
    },
    {
      title: 'Particular',
      render: (rowData: any) =>
        rowData?.hirechart?.particulars_type
          ? `${rowData?.hirechart?.particulars} ${rowData?.hirechart?.particulars_type}`
          : rowData?.hirechart?.particulars || '',
    },
    {
      title: '8 Hrs / 100 KMs',
      field: 'kms',
      render: (rowData: any) => `${rowData?.bill?.kms || 0} `,
    },
    {
      title: 'Ex Kms',
      field: 'xkms',
      render: (rowData: any) => `${rowData?.bill?.xkms || 0} `,
    },
    {
      title: 'Rate',
      field: 'rate',
      render: (rowData: any) => `${rowData?.bill?.rate || 0} `,
    },
    {
      title: 'Tot',
      field: 'tot1',
      render: (rowData: any) => `${rowData?.bill?.tot1 || 0} `,
    },
    {
      title: 'Ex HRs',
      field: 'xhrs',
      render: (rowData: any) => `${rowData?.bill?.xhrs || 0} `,
    },
    {
      title: 'Wait Ch',
      field: 'wchr',
      render: (rowData: any) => `${rowData?.bill?.wchr || 0} `,
    },
    {
      title: 'Tot',
      field: 'tot2',
      render: (rowData: any) => `${rowData?.bill?.tot2 || 0} `,
    },
    {
      title: 'Early',
      field: 'early',
      render: (rowData: any) => `${rowData?.bill?.early || 0} `,
    },
    {
      title: 'Early Morning',
      field: 'early_morning',
      render: (rowData: any) => `${rowData?.bill?.early_morning || 0} `,
    },
    {
      title: 'Late',
      field: 'late',
      render: (rowData: any) => `${rowData?.bill?.late || 0} `,
    },
    {
      title: 'Nite',
      field: 'onite',
      render: (rowData: any) => `${rowData?.bill?.onite || 0} `,
    },
    {
      title: 'Extra',
      field: 'toll',
      render: (rowData: any) => `${rowData?.bill?.toll || 0} `,
    },
    {
      title: 'Total',
      field: 'tot3',
      render: (rowData: any) => `${rowData?.bill?.tot3 || 0} `,
    },
    {
      title: 'billId',
      field: 'billId',
      hidden: true,
      render: (rowData: any) => `${rowData?.billData?.id} `,
    },
    {
      title: 'billNo',
      field: 'billNo',
      hidden: true,
      render: (rowData: any) => `${rowData?.hirechartData?.billno} `,
    },
  ];

  return (
    <Grid
      container
      direction="column"
      spacing={1}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '0.5rem', sm: '1rem' },
      }}
    >
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle text="Bill Register Details" />
      </Grid>

      <Grid item xs container spacing={2}>
        <Paper elevation={2} sx={{ width: '100%' }}>
          <Grid container spacing={2}>
            <Grid item xs={5} md={5} marginLeft={2} marginTop={4}>
              <Typography variant="h6">
                Date: {formatDate(billReg?.date)}
              </Typography>
              <Typography variant="h6">Bill No: {billReg?.billnum}</Typography>
              <Typography variant="h6">Company: {details?.company}</Typography>
              <Typography variant="h6">
                Parent Company: {details?.cname}
              </Typography>
              <Typography variant="h6">Address: {details?.address}</Typography>
              <Typography variant="h6">GST Number: {details?.gstno}</Typography>
              <Typography variant="h6">PAN Number: {details?.panno}</Typography>
              <Typography variant="h6">
                Subagent: {details?.subagent}
              </Typography>
              <Typography variant="h6">Ref: {billReg?.ref}</Typography>
            </Grid>
            <Grid
              item
              xs={5}
              md={5}
              container
              direction="column"
              marginLeft={14}
              marginTop={4}
            >
              <Grid item py={2} marginLeft={14}>
                <Select
                  labelId="paid"
                  id="paid"
                  value={paid}
                  onChange={e => setPaid(e.target.value)}
                  label="Paid"
                >
                  <MenuItem value="0">Unpaid</MenuItem>
                  <MenuItem value="1">Paid</MenuItem>
                </Select>
              </Grid>
              <Grid item py={4} marginLeft={14}>
                <TextField
                  id="premark"
                  value={premark}
                  onChange={e => setPremark(e.target.value)}
                  label="Remarks"
                  variant="outlined"
                  multiline
                  rows={3}
                />
              </Grid>
              <Grid item marginLeft={14}>
                <Button variant="contained" onClick={updateContractDetails}>
                  Update
                </Button>
              </Grid>
            </Grid>
          </Grid>

          <Grid item xs={12}>
            <MaterialTable
              title=""
              columns={columns}
              data={hirechartList || []}
              isLoading={isLoading}
              options={{
                draggable: false,
                paging: false,
                search: true,
                tableLayout: 'fixed',
                searchFieldVariant: 'outlined',
              }}
              style={{
                minHeight: 'calc(100vh - 180px)',
                overflowY: 'auto',
              }}
              components={{
                Container: props => <Paper elevation={0} {...props} />,
              }}
              actions={[
                {
                  icon: EditIcon,
                  tooltip: 'Edit Bill',
                  onClick: (_, rowData) => handleEdit(rowData),
                },
                {
                  icon: DeleteIcon,
                  tooltip: 'Delete Bill',
                  onClick: (_, rowData) => handleDelete(rowData),
                },
              ]}
            />
          </Grid>

          <Grid
            item
            container
            direction="column"
            justifyContent="flex-end"
            alignItems="flex-end"
            paddingRight={4}
          >
            <Grid item>
              <strong>Total (Rs.) </strong>
              {total}
            </Grid>
            <Grid item>
              <strong>Less </strong>
              {discount}
            </Grid>
            <Grid item>
              <strong>Net Taxable </strong>
              {netTaxable}
            </Grid>
            <Grid item>
              <strong>Add {gstValue}</strong>
              {gstAmount}
            </Grid>
            <Grid item>
              <strong>G. Total </strong>
              {grandTotal}
            </Grid>
          </Grid>

          <Box>
            <Box sx={{ mt: 2, mb: 2 }} marginLeft={2}>
              <Typography style={{ marginLeft: '4px' }} variant="h6">
                Add Additional Charges
              </Typography>
              <TextField
                id="additionalParticular"
                value={additionalParticular}
                onChange={e => setAdditionalParticular(e.target.value)}
                label="Particular"
                variant="outlined"
                style={{ marginLeft: '12px' }}
              />
              <TextField
                id="additionalAmount"
                value={additionalAmount}
                onChange={e => setAdditionalAmount(e.target.value)}
                label="Amount"
                variant="outlined"
                style={{ marginLeft: '12px' }}
              />
              <Button
                style={{ marginLeft: '12px' }}
                variant="contained"
                onClick={updateAdditionalDetails}
              >
                Add
              </Button>
            </Box>
          </Box>
          <Box>
            <Box sx={{ mt: 2, mb: 2 }} marginLeft={2}>
              <Typography style={{ marginLeft: '4px' }} variant="h6">
                Discount
              </Typography>
              <TextField
                id="amount"
                value={discountAmount}
                onChange={e => setDiscountAmount(e.target.value)}
                label="Amount"
                variant="outlined"
                style={{ marginLeft: '12px' }}
              />
              <Button
                style={{ marginLeft: '12px' }}
                variant="contained"
                onClick={updateDiscountDetails}
              >
                Add
              </Button>
            </Box>
          </Box>
        </Paper>
      </Grid>

      <DeleteDialog
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        handleConfirmDelete={handleConfirmDelete}
      />
    </Grid>
  );
};

export default BillRegisterDetail;
