import { useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';
import { useParams } from 'react-router-dom';

import {
  Box,
  Button,
  Grid,
  Paper, // Table,
  // TableBody,
  // TableCell,
  // TableContainer,
  // TableHead,
  // TableRow,
  TextField,
  Typography,
} from '@mui/material';

import PageTitle from '@components/PageTitle';

import {
  // useReadBillReg,
  // useReadBills,
  useReadDBillHires, // useUpdateBillReg,
  useUpdateDBillHire,
} from '../../hooks/bill';

// import { useReadExtras } from '../../hooks/extra';
// import { useReadHireCharts } from '../../hooks/hirechart';
// import { useReadPtolls } from '../../hooks/ptoll';

const NewBillEditHire = () => {
  const { enqueueSnackbar } = useSnackbar();
  const { id } = useParams();

  /* const [deleteDialogOpen, setDeleteDialogOpen] = useState(false); */

  const { data: bills } = useReadDBillHires();

  useEffect(() => {
    if (bills) {
      const dbBill = bills?.find(b => Number(b.billnum) === Number(id));
      if (dbBill) {
        setDbHireId(dbBill.id);
        setFromDate(dbBill.date);
        setVtype(dbBill.vtype);
        setVno(dbBill.vno);
        setParticular(dbBill.particulars);
        // setMintype(dbBill.minType)
        setKms(dbBill.initial);
        setExtraKms(dbBill.xkm);
        setRate(dbBill.rate);
        setTotal(dbBill.tot1);
        setExtraHrs(dbBill.xhr);
        setWaitChgs(dbBill.wch);
        setTotal2(dbBill.tot2);
        setEarly(dbBill.early);
        setEarlyMorning(dbBill.early_morning);
        setLate(dbBill.late);
        setOvernight(dbBill.onite);
        setExtras(dbBill.extra);
        setTotal3(dbBill.total);
        setBillNo(dbBill.billnum);
      }
    }
    // eslint-disable-next-line
  }, [bills]);

  const [billNo, setBillNo] = useState('');
  const [dbHireId, setDbHireId] = useState('');

  const [fromDate, setFromDate] = useState('');
  const [vtype, setVtype] = useState('');
  const [vno, setVno] = useState('');
  const [particular, setParticular] = useState('');
  const [minType] = useState('');
  const [kms, setKms] = useState('');
  const [rate, setRate] = useState('');
  const [extraKms, setExtraKms] = useState('');
  const [total, setTotal] = useState('');
  const [extraHrs, setExtraHrs] = useState('');
  const [waitChgs, setWaitChgs] = useState('');
  const [total2, setTotal2] = useState('');
  const [early, setEarly] = useState('');
  const [earlyMorning, setEarlyMorning] = useState('');
  const [late, setLate] = useState('');
  const [overnight, setOvernight] = useState('');
  const [extras, setExtras] = useState('');
  const [total3, setTotal3] = useState('');

  // const { mutate: useGetDBillHireByBillNoMutate } = useGetDBillHireByBillNo();
  const { mutate: useUpdateDBillHireMutate } = useUpdateDBillHire();

  // const navigate = useNavigate();

  // eslint-disable-next-line
  const addBillDetails = () => {
    // eslint-disable-next-line
    useUpdateDBillHireMutate(
      {
        id: dbHireId,
        date: fromDate,
        vtype,
        vno,
        particulars: particular,
        minType,
        initial: kms,
        xkm: extraKms,
        rate,
        tot1: total,
        xhr: extraHrs,
        wch: waitChgs,
        tot2: total3,
        early,
        early_morning: earlyMorning,
        late,
        onite: overnight,
        extra: extras,
        total: total3,
        billnum: billNo,
      },
      {
        onSuccess: () => {
          enqueueSnackbar('Content Updated', { variant: 'info' });
        },
        onError: () => {
          enqueueSnackbar('Failed to Update Content', { variant: 'error' });
        },
      }
    );
  };

  return (
    <Grid
      container
      direction="column"
      spacing={1}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '0.5rem', sm: '1rem' },
      }}
    >
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle text="New Bill" />
      </Grid>

      <Paper elevation={2} sx={{ width: '100%' }}>
        <div id="printData">
          <Box sx={{ p: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={12}>
                <Typography variant="h4">Update Hire</Typography>
              </Grid>
              <Grid item xs={12} md={12}>
                <Box>
                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <Box>
                      <TextField
                        label="Date"
                        type="date"
                        value={fromDate}
                        onChange={e => setFromDate(e.target.value)}
                        InputLabelProps={{
                          shrink: true,
                        }}
                        fullWidth
                      />
                    </Box>
                  </Box>
                  <Box my={4} gap={4}>
                    <Box>
                      <TextField
                        id="vtype"
                        value={vtype}
                        onChange={e => setVtype(e.target.value)}
                        label="Veh Type"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                  <Box my={4} gap={4}>
                    <Box>
                      <TextField
                        id="vno"
                        value={vno}
                        onChange={e => setVno(e.target.value)}
                        label="Veh No"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                  <Box my={4} gap={4}>
                    <Box>
                      <TextField
                        id="particular"
                        value={particular}
                        onChange={e => setParticular(e.target.value)}
                        label="Particulars"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <Box marginRight={3} paddingRight={4}>
                      <Typography>8 HRs / 100 KMs</Typography>
                    </Box>
                    <Box marginLeft={3} paddingLeft={4}>
                      <TextField
                        id="kms"
                        value={kms}
                        onChange={e => setKms(e.target.value)}
                        label="Kms"
                        variant="outlined"
                      />
                    </Box>
                  </Box>

                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <Box>
                      <TextField
                        id="extraKms"
                        value={extraKms}
                        onChange={e => setExtraKms(e.target.value)}
                        label="Extra Kms"
                        variant="outlined"
                      />
                    </Box>
                    <Box>
                      <TextField
                        id="rate"
                        value={rate}
                        onChange={e => setRate(e.target.value)}
                        label="Rate"
                        variant="outlined"
                      />
                    </Box>

                    <Box>
                      <TextField
                        id="total"
                        value={total}
                        onChange={e => setTotal(e.target.value)}
                        label="Total"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <Box>
                      <TextField
                        id="extraHrs"
                        value={extraHrs}
                        onChange={e => setExtraHrs(e.target.value)}
                        label="Extra Hours"
                        variant="outlined"
                      />
                    </Box>
                    <Box>
                      <TextField
                        id="waitChgs"
                        value={waitChgs}
                        onChange={e => setWaitChgs(e.target.value)}
                        label="Wait Charges"
                        variant="outlined"
                      />
                    </Box>
                    <Box>
                      <TextField
                        id="total2"
                        value={total2}
                        onChange={e => setTotal2(e.target.value)}
                        label="Total"
                        variant="outlined"
                      />
                    </Box>
                  </Box>

                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <Box>
                      <TextField
                        id="early"
                        value={early}
                        onChange={e => setEarly(e.target.value)}
                        label="Early"
                        variant="outlined"
                      />
                    </Box>
                    <Box>
                      <TextField
                        id="earlyMorning"
                        value={earlyMorning}
                        onChange={e => setEarlyMorning(e.target.value)}
                        label="Early Morning"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <Box>
                      <TextField
                        id="late"
                        value={late}
                        onChange={e => setLate(e.target.value)}
                        label="Late"
                        variant="outlined"
                      />
                    </Box>
                    <Box>
                      <TextField
                        id="overnight"
                        value={overnight}
                        onChange={e => setOvernight(e.target.value)}
                        label="Overnight"
                        variant="outlined"
                      />
                    </Box>
                  </Box>
                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <TextField
                      id="extras"
                      value={extras}
                      onChange={e => setExtras(e.target.value)}
                      label="Extras"
                      variant="outlined"
                    />
                  </Box>
                  <Box my={4} gap={4} alignItems="center" display="flex">
                    <TextField
                      id="total3"
                      value={total3}
                      onChange={e => setTotal3(e.target.value)}
                      label="Total"
                      variant="outlined"
                    />
                  </Box>
                  <Box>
                    <Button variant="contained" onClick={addBillDetails}>
                      Update
                    </Button>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </div>
      </Paper>
    </Grid>
  );
};

export default NewBillEditHire;
