import { useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';

import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { Grid, Paper } from '@mui/material';

import MaterialTable, { Column } from '@material-table/core';

import DeleteDialog from '@components/Dialog/DeleteDialog';
import DetailDialog from '@components/Dialog/DetailDialog';
import EditDialog from '@components/Dialog/EditDialog';
import EditPdcHire from '@components/HireCharts/EditPdcHire';
import HireChartDetails from '@components/HireCharts/HireChartDetails';
import PageTitle from '@components/PageTitle';

import { useReadExtras } from '../../hooks/extra';
import {
  useDeleteHireChart,
  useEditHireChart,
  useReadHireCharts,
} from '../../hooks/hirechart';
import {
  useAddPtoll,
  useDeletePtollByHid,
  useReadPtolls,
} from '../../hooks/ptoll';
import { IHireChart } from '../../types/hirechart';

const BreakdownIncidence = () => {
  const { data: hirecharts, isLoading, refetch } = useReadHireCharts();
  const [srlocalhirechart, setSRLocalHirechart] = useState<IHireChart[]>([]);

  const [localhirechart, setLocalHirechart] = useState<IHireChart[]>([]);
  const [hirechart, setHireChart] = useState<IHireChart | null>(null);
  const [defaultvalue, setDefaultvalue] = useState<any>();
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);

  const { enqueueSnackbar } = useSnackbar();
  const { mutate: deleteHirechartMutate } = useDeleteHireChart();
  const { mutate: editHirechartMutate } = useEditHireChart();
  const { mutate: deletePtollByHid } = useDeletePtollByHid(); // renamed to avoid 'use' prefix
  const { mutate: addPtoll } = useAddPtoll(); // renamed to avoid 'use' prefix
  const { data: ptolls } = useReadPtolls();
  const { data: extraData = [] } = useReadExtras();

  useEffect(() => {
    document.title = 'Reports - nunes';
    if (localhirechart) {
      const hirechartsWithSr = localhirechart.map((hireItems, index) => ({
        ...hireItems,
        sr: index + 1,
      }));
      setSRLocalHirechart(hirechartsWithSr);
    }
  }, [localhirechart]);

  useEffect(() => {
    document.title = 'Reports - nunes';
    if (hirecharts) {
      const processedData = hirecharts
        .filter(item => item.break !== '0')
        .map(item => ({
          ...item,
          elo: getELO(item),
        }));
      setLocalHirechart(processedData);
    }
  }, [hirecharts]);

  const formatDate = (dateStr: string) => {
    try {
      const date = new Date(dateStr);
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const year = String(date.getFullYear()).slice(-2);
      return `${day}-${month}-${year}`;
    } catch (error) {
      console.error('Error parsing date:', error);
      return dateStr;
    }
  };

  const getELO = (item: any) => {
    let elo = '';
    if (item.early === '1') elo = 'E';
    if (item.early_morning === '1') elo = 'EM';
    if (item.late === '1') elo += ' L';
    if (item.onite === '1') elo += ' O';
    return elo.trim();
  };

  const handleDetails = (rowData: any) => {
    if (rowData) {
      setHireChart(rowData);
      setDrawerOpen(true);
    }
  };

  const handleEdit = (rowData: any) => {
    setHireChart(rowData);
    const selectedExtras = ptolls?.filter(
      (toll: any) => toll?.hid === rowData?.id
    );

    const extrasData = extraData?.map((item: any) => ({
      ...item,
      checked: !!selectedExtras?.filter((extra: any) => extra.eid === item.id)
        ?.length,
    }));
    setDefaultvalue({
      id: rowData?.id,
      date: rowData?.date,
      company: rowData?.company,
      ac: rowData?.ac,
      vtype: rowData?.vtype,
      ocode: rowData?.ocode,
      vno: rowData?.vno,
      driver: rowData?.driver,
      repveh: rowData?.repveh,
      repdriver: rowData?.repdriver,
      cleaner: rowData?.cleaner,
      hiretype: rowData?.hiretype,
      client: rowData?.client,
      subagent: rowData?.subagent,
      rvocode: rowData?.rvocode,
      okm: rowData?.okm,
      ckm: rowData?.ckm,
      tkm: rowData?.tkm,
      akm: rowData?.akm,
      ttkm: rowData?.ttkm,
      bill: rowData?.bill,
      billno: rowData?.billno,
      extra_toll: rowData?.extra_toll,
      extras: extrasData,
      toll: rowData?.toll,
      tollc: rowData?.tollc,
      break: rowData?.break,
      remark: rowData?.remark,
      particulars: rowData?.particulars,
      particulars_type: rowData?.particulars_type,
      early: rowData?.early === '1',
      early_morning: rowData?.early_morning === '1',
      late: rowData?.late === '1',
      onite: rowData?.onite === '1',
    });
    setEditDialogOpen(true);
  };

  const handleDelete = (rowData: any) => {
    setHireChart(rowData);
    setDeleteDialogOpen(true);
  };

  const handleCloseEditDialog = (action: any) => {
    if (hirechart && action && action?.action) {
      const extrasTotal = action?.formData?.extras
        ?.filter((d: any) => d?.checked === true)
        ?.reduce((acc: any, cum: any) => acc + (cum?.rate ? +cum.rate : 0), 0);

      const toll =
        (extrasTotal || 0) +
        (action.formData.extra_toll ? +action.formData.extra_toll : 0);

      const formData = {
        ...action.formData,
        id: +hirechart.id,
        toll,
      };

      const extras = formData.extras?.filter((ex: any) => ex?.checked === true);
      delete formData.extras;

      editHirechartMutate(formData, {
        onSuccess: () => {
          deletePtollByHid(
            {
              hid: +hirechart.id,
            },
            {
              onSuccess: () => {
                const addPtollArr: any = [];

                extras?.forEach((ex: any) => {
                  if (ex.id) {
                    addPtollArr.push({
                      hid: +hirechart.id,
                      eid: ex.id,
                      rate: ex.rate,
                    });
                  }
                });

                addPtoll(addPtollArr, {
                  onSuccess: () => {
                    refetch();
                    enqueueSnackbar('Content Updated', { variant: 'info' });
                  },
                });
              },
            }
          );
        },
      });
    }
    setEditDialogOpen(false);
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  const handleConfirmDelete = () => {
    if (hirechart) {
      deleteHirechartMutate(
        { id: +hirechart.id },
        {
          onSuccess: () => {
            setLocalHirechart(prevHirechart =>
              prevHirechart.filter(c => c.id !== hirechart.id)
            );
            enqueueSnackbar('Content Deleted', { variant: 'success' });
          },
          onError: () => {
            enqueueSnackbar('Failed to Delete Content', { variant: 'error' });
          },
        }
      );
    }
    setDeleteDialogOpen(false);
  };

  const columns: Column<IHireChart>[] = [
    {
      title: 'S.No',
      field: 'sr',
      width: '50px',
      headerStyle: {
        textAlign: 'center',
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
        paddingLeft: '12px',
      },
      cellStyle: {
        textAlign: 'center',
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Date',
      field: 'date',
      defaultSort: 'desc',
      render: rowData => formatDate(rowData.date),
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Company',
      field: 'company',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Veh Type',
      field: 'vtype',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Own Code',
      field: 'ocode',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Veh No.',
      field: 'vno',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Rep Veh',
      field: 'repveh',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Rep Own Code',
      field: 'rvocode',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Particulars',
      render: rowData => `${rowData.particulars} ${rowData.particulars_type}`,
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '200px',
      },
    },
    {
      title: 'Hire Type',
      field: 'hiretype',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Sub Agent',
      field: 'subagent',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '150px',
      },
    },
    {
      title: 'Client',
      field: 'client',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '150px',
      },
    },
    {
      title: 'E/EM/L/O',
      field: 'elo',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Toll',
      field: 'toll',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Bill No.',
      field: 'billno',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
    },
  ];

  return (
    <Grid
      container
      direction="column"
      spacing={1}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '0.5rem', sm: '1rem' },
      }}
    >
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle text="Breakdown Incidence - Report" />
      </Grid>

      <Grid item xs container spacing={2}>
        <Paper elevation={2} sx={{ width: '100%' }}>
          <Grid item xs={12}>
            <MaterialTable
              title=""
              columns={columns}
              data={srlocalhirechart || []}
              isLoading={isLoading}
              options={{
                draggable: false,
                paging: true,
                pageSize: 20,
                pageSizeOptions: [10, 25, 50, 100],
                search: true,
                tableLayout: 'fixed',
                searchFieldVariant: 'outlined',
                actionsColumnIndex: -1,
              }}
              style={{
                minHeight: 'calc(100vh - 180px)',
                overflowY: 'auto',
              }}
              components={{
                Container: props => <Paper elevation={0} {...props} />,
              }}
              onRowClick={(_, rowData) => handleDetails(rowData)}
              actions={[
                {
                  icon: EditIcon,
                  tooltip: 'Edit',
                  onClick: (_, rowData) => handleEdit(rowData),
                },
                {
                  icon: DeleteIcon,
                  tooltip: 'Delete',
                  onClick: (_, rowData) => handleDelete(rowData),
                },
              ]}
            />
          </Grid>
        </Paper>
      </Grid>

      <DetailDialog
        open={drawerOpen}
        handleCloseDrawer={() => setDrawerOpen(false)}
        ContentComponent={HireChartDetails}
        data={hirechart}
      />

      <EditDialog
        open={editDialogOpen}
        handleClose={handleCloseEditDialog}
        ContentComponent={EditPdcHire}
        billMade={false}
        defaultvalue={defaultvalue}
      />

      <DeleteDialog
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        handleConfirmDelete={handleConfirmDelete}
      />
    </Grid>
  );
};

export default BreakdownIncidence;
