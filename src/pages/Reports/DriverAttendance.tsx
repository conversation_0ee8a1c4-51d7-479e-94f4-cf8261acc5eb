import { useState } from 'react';

import { useNavigate } from 'react-router-dom';

import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import RefreshIcon from '@mui/icons-material/Refresh';
import SearchIcon from '@mui/icons-material/Search';
import VisibilityIcon from '@mui/icons-material/Visibility';
import {
  Box,
  Button,
  Grid,
  IconButton,
  Paper,
  TextField,
  Tooltip,
  Typography,
} from '@mui/material';
import { green } from '@mui/material/colors';

import MaterialTable, { Column } from '@material-table/core';

import PageTitle from '@components/PageTitle';

import { useDriverAttendance } from '../../hooks/driverAttendance';

interface AttendanceStats {
  id: number;
  name: string;
  totalHires: number;
  totalAbsents: number;
}

const DriverAttendance = () => {
  const navigate = useNavigate();
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [searchParams, setSearchParams] = useState<{
    startDate: string;
    endDate: string;
  }>({
    startDate: '',
    endDate: '',
  });

  const {
    data: stats,
    isLoading,
    refetch,
  } = useDriverAttendance({
    startDate: searchParams.startDate,
    endDate: searchParams.endDate,
  });

  const handleSearch = () => {
    if (startDate && endDate) {
      setSearchParams({
        startDate,
        endDate,
      });
      refetch();
    }
  };

  const handleReset = () => {
    setStartDate('');
    setEndDate('');
    setSearchParams({
      startDate: '',
      endDate: '',
    });
    refetch();
  };

  const columns: Column<AttendanceStats>[] = [
    {
      title: '#',
      field: 'serialNumber',
      render: rowData => (stats?.indexOf(rowData) ?? 0) + 1,
      width: '50px',
      cellStyle: {
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Driver Name',
      field: 'name',
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '150px',
      },
    },
    {
      title: 'Total Hires',
      field: 'totalHires',
      type: 'numeric' as const,
      render: (rowData: AttendanceStats) => Number(rowData.totalHires) || 0,
      cellStyle: {
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Total Absents',
      field: 'totalAbsents',
      type: 'numeric' as const,
      render: (rowData: AttendanceStats) => Number(rowData.totalAbsents) || 0,
      cellStyle: {
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Actions',
      field: 'actions',
      sorting: false,
      width: '50px',
      render: (rowData: AttendanceStats) => (
        <Tooltip title="View Attendance Details">
          <IconButton
            size="small"
            onClick={() => navigate(`/reports/driver-attendance/${rowData.id}`)}
          >
            <VisibilityIcon />
          </IconButton>
        </Tooltip>
      ),
    },
  ];

  return (
    <Box sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
      <Box sx={{ fontSize: '1.5rem', fontWeight: 500 }}>
        <PageTitle text="Driver Attendance Overview" />
      </Box>

      <Paper
        elevation={2}
        sx={{
          p: 3,
          mb: 3,
          backgroundColor: '#f8f9fa',
          borderRadius: 2,
        }}
      >
        <Typography
          variant="h6"
          sx={{
            mb: 2,
            color: green[300],
            fontWeight: 500,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
          }}
        >
          <CalendarTodayIcon fontSize="small" />
          Select Date Range
        </Typography>

        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} sm={4}>
            <TextField
              label="Start Date"
              type="date"
              value={startDate}
              onChange={e => setStartDate(e.target.value)}
              fullWidth
              InputLabelProps={{
                shrink: true,
              }}
              sx={{
                backgroundColor: '#fff',
                borderRadius: 1,
                '& .MuiOutlinedInput-root': {
                  '&:hover fieldset': {
                    borderColor: green[300],
                  },
                },
              }}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              label="End Date"
              type="date"
              value={endDate}
              onChange={e => setEndDate(e.target.value)}
              fullWidth
              InputLabelProps={{
                shrink: true,
              }}
              sx={{
                backgroundColor: '#fff',
                borderRadius: 1,
                '& .MuiOutlinedInput-root': {
                  '&:hover fieldset': {
                    borderColor: green[300],
                  },
                },
              }}
            />
          </Grid>
          <Grid item xs={12} sm={2}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SearchIcon />}
              onClick={handleSearch}
              fullWidth
              sx={{
                height: '40px',
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '1rem',
                borderRadius: '2rem',
                backgroundColor: green[300],
                '&:hover': {
                  backgroundColor: green[400],
                },
              }}
            >
              Search
            </Button>
          </Grid>
          <Grid item xs={12} sm={2}>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<RefreshIcon />}
              onClick={handleReset}
              fullWidth
              sx={{
                height: '40px',
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '1rem',
                borderRadius: '2rem',
                borderColor: green[300],
                color: green[300],
                padding: '6px 16px',
                '&:hover': {
                  borderColor: green[400],
                  backgroundColor: 'transparent',
                },
              }}
            >
              Reset
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <Paper
        elevation={2}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
        }}
      >
        <MaterialTable
          title=""
          columns={columns}
          data={stats || []}
          isLoading={isLoading}
          options={{
            draggable: false,
            paging: true,
            pageSize: 10,
            pageSizeOptions: [10, 20, 30, 50],
            search: true,
            tableLayout: 'fixed',
            searchFieldVariant: 'outlined',
            headerStyle: {
              backgroundColor: '#f5f5f5',
              fontWeight: 'bold',
              whiteSpace: 'nowrap',
            },
            maxBodyHeight: 'calc(100vh - 280px)',
          }}
          style={{
            width: '100%',
            overflowX: 'auto',
          }}
          components={{
            Container: props => (
              <Paper
                elevation={0}
                {...props}
                sx={{
                  borderRadius: '8px',
                  overflow: 'hidden',
                }}
              />
            ),
            Toolbar: () => <div style={{ margin: '10px' }}> </div>,
          }}
        />
      </Paper>
    </Box>
  );
};

export default DriverAttendance;
