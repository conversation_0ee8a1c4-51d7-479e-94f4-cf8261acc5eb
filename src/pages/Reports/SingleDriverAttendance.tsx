import { useEffect, useMemo, useState } from 'react';

import { useParams } from 'react-router-dom';

import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import RefreshIcon from '@mui/icons-material/Refresh';
import SearchIcon from '@mui/icons-material/Search';
import { Box, Button, Grid, Paper, TextField, Typography } from '@mui/material';
import { green } from '@mui/material/colors';

import MaterialTable, { Column } from '@material-table/core';

import PageTitle from '@components/PageTitle';

import { getDefaultDateRange } from '../../helpers/dateFilters';
import { useMonthlyDateReset } from '../../helpers/useMonthlyDateReset';
import { useSingleDriverAttendance } from '../../hooks/driverAttendance';

interface AttendanceDay {
  date: string;
  status: 'Present' | 'Absent';
  hireCount: number;
}

interface AttendanceResponse {
  driverName: string;
  attendance: AttendanceDay[];
}

interface SearchParams {
  startDate: string;
  endDate: string;
  driverId?: string;
}

const SingleDriverAttendance = () => {
  const { driverId } = useParams<{ driverId: string }>();

  // Get saved date range from localStorage
  const savedDateRange = (() => {
    const savedData = localStorage.getItem(
      'SingleDriverAttendanceFilteredDateRange'
    );
    return savedData ? JSON.parse(savedData) : getDefaultDateRange();
  })();

  const [dateRange, setDateRange] = useState(savedDateRange);
  useMonthlyDateReset(setDateRange, 'SingleDriverAttendanceFilteredDateRange');

  const [startDate, setStartDate] = useState(savedDateRange.startDate);
  const [endDate, setEndDate] = useState(savedDateRange.endDate);
  const [searchParams, setSearchParams] = useState<SearchParams>({
    startDate: savedDateRange.startDate,
    endDate: savedDateRange.endDate,
    driverId: driverId || '',
  });

  useEffect(() => {
    setStartDate(dateRange.startDate);
    setEndDate(dateRange.endDate);
    setSearchParams({
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
    });
  }, [dateRange]);

  useEffect(() => {
    localStorage.setItem(
      'SingleDriverAttendanceFilteredDateRange',
      JSON.stringify({
        startDate,
        endDate,
      })
    );
  }, [startDate, endDate]);

  const {
    data: attendanceData,
    isLoading,
    refetch,
  } = useSingleDriverAttendance(Number(driverId), searchParams);

  const driverName = useMemo(
    () => (attendanceData as AttendanceResponse)?.driverName,
    [attendanceData]
  );

  useEffect(() => {
    if (driverName) {
      document.title = `Attendance - ${driverName}`;
    }
    return () => {
      document.title = 'Nunes';
    };
  }, [driverName]);

  const handleSearch = () => {
    if (startDate && endDate) {
      setSearchParams({
        startDate,
        endDate,
      });
    }
  };

  const handleReset = () => {
    const defaultRange = getDefaultDateRange();

    setDateRange(defaultRange);
    setStartDate(defaultRange.startDate);
    setEndDate(defaultRange.endDate);

    const currentDriverId = driverId;

    setSearchParams({
      startDate: defaultRange.startDate,
      endDate: defaultRange.endDate,
      driverId: currentDriverId || '',
    });

    localStorage.removeItem('SingleDriverAttendanceFilteredDateRange');

    if (currentDriverId) {
      refetch();
    }
  };

  const columns: Column<AttendanceDay>[] = [
    {
      title: '#',
      field: 'serialNumber',
      render: (data: AttendanceDay & { tableData?: { id: number } }) =>
        (data.tableData?.id ?? 0) + 1,
      width: '50px',
      cellStyle: {
        whiteSpace: 'nowrap',
        padding: '10px 16px',
      },
    },
    {
      title: 'Date',
      field: 'date',
      cellStyle: {
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        maxWidth: '150px',
        padding: '12px 16px',
      },
    },
    {
      title: 'Status',
      field: 'status',
      cellStyle: (_, rowData) => ({
        whiteSpace: 'nowrap',
        color: rowData.status === 'Present' ? '#1976d2' : '#d32f2f',
        padding: '12px 16px',
      }),
    },
    {
      title: 'Hire Count',
      field: 'hireCount',
      type: 'numeric' as const,
      cellStyle: {
        whiteSpace: 'nowrap',
        padding: '12px 16px',
      },
    },
  ];

  return (
    <Box sx={{ p: { xs: 1, sm: 2, md: 3 } }}>
      <Box sx={{ fontSize: '1.5rem', fontWeight: 500 }}>
        <PageTitle
          text={
            isLoading
              ? 'Loading driver details...'
              : `Driver Attendance - ${(attendanceData as AttendanceResponse)?.driverName || 'Unknown Driver'}`
          }
        />
      </Box>

      <Paper
        elevation={2}
        sx={{
          p: 3,
          mb: 3,
          backgroundColor: '#f8f9fa',
          borderRadius: 2,
        }}
      >
        <Typography
          variant="h6"
          sx={{
            mb: 2,
            color: green[300],
            fontWeight: 500,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
          }}
        >
          <CalendarTodayIcon fontSize="small" />
          Select Date Range
        </Typography>

        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} sm={4}>
            <TextField
              label="Start Date"
              type="date"
              value={startDate}
              onChange={e => setStartDate(e.target.value)}
              fullWidth
              InputLabelProps={{
                shrink: true,
              }}
              sx={{
                backgroundColor: '#fff',
                borderRadius: 1,
                '& .MuiOutlinedInput-root': {
                  '&:hover fieldset': {
                    borderColor: green[300],
                  },
                },
              }}
            />
          </Grid>
          <Grid item xs={12} sm={4}>
            <TextField
              label="End Date"
              type="date"
              value={endDate}
              onChange={e => setEndDate(e.target.value)}
              fullWidth
              InputLabelProps={{
                shrink: true,
              }}
              sx={{
                backgroundColor: '#fff',
                borderRadius: 1,
                '& .MuiOutlinedInput-root': {
                  '&:hover fieldset': {
                    borderColor: green[300],
                  },
                },
              }}
            />
          </Grid>
          <Grid item xs={12} sm={2}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SearchIcon />}
              onClick={handleSearch}
              fullWidth
              sx={{
                height: '40px',
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '1rem',
                borderRadius: '2rem',
                backgroundColor: green[300],
                '&:hover': {
                  backgroundColor: green[400],
                },
              }}
            >
              Search
            </Button>
          </Grid>
          <Grid item xs={12} sm={2}>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<RefreshIcon />}
              onClick={handleReset}
              fullWidth
              sx={{
                height: '40px',
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '1rem',
                borderRadius: '2rem',
                borderColor: green[300],
                color: green[300],
                '&:hover': {
                  borderColor: green[400],
                  backgroundColor: 'transparent',
                },
              }}
            >
              Reset
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <Paper
        elevation={2}
        sx={{
          borderRadius: 2,
          overflow: 'hidden',
        }}
      >
        <MaterialTable
          title=""
          columns={columns}
          data={(attendanceData as AttendanceResponse)?.attendance || []}
          isLoading={isLoading}
          options={{
            draggable: false,
            paging: true,
            pageSize: 10,
            pageSizeOptions: [10, 20, 30, 50],
            search: true,
            tableLayout: 'fixed',
            searchFieldVariant: 'outlined',
            headerStyle: {
              backgroundColor: '#f5f5f5',
              fontWeight: 'bold',
              whiteSpace: 'nowrap',
            },
            maxBodyHeight: 'calc(100vh - 280px)',
          }}
          style={{
            width: '100%',
            overflowX: 'auto',
          }}
          components={{
            Container: props => (
              <Paper
                elevation={0}
                {...props}
                sx={{
                  borderRadius: '8px',
                  overflow: 'hidden',
                }}
              />
            ),
            Toolbar: () => <div style={{ margin: '10px' }}> </div>,
          }}
        />
      </Paper>
    </Box>
  );
};

export default SingleDriverAttendance;
