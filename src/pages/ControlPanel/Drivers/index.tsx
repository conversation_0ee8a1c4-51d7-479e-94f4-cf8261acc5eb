import { useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';

import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import PersonOffIcon from '@mui/icons-material/PersonOff';
import { Button, Grid, Paper } from '@mui/material';

import MaterialTable, { Column } from '@material-table/core';

import AbsenceModal from '@components/ControlPanel/Drivers/AbsenceModal';
import AddDriver from '@components/ControlPanel/Drivers/AddDriver';
import DriverDetail from '@components/ControlPanel/Drivers/DriverDetail';
import EditDriver from '@components/ControlPanel/Drivers/EditDriver';
import AddDialog from '@components/Dialog/AddDialog';
import DeleteDialog from '@components/Dialog/DeleteDialog';
import DetailDialog from '@components/Dialog/DetailDialog';
import EditDialog from '@components/Dialog/EditDialog';
import PageTitle from '@components/PageTitle';

import {
  useAddDrivers,
  useDeleteDrivers,
  useEditDrivers,
  useReadDrivers,
  useRecordDriverAbsence,
} from '../../../hooks/controlpanel/drivers';
import { IDrivers } from '../../../types/controlpanel/drivers';

const Drivers = () => {
  const { data: drivers, isLoading, refetch } = useReadDrivers();

  const [localdriver, setLocalDriver] = useState<IDrivers[]>([]);
  const [driver, setDriver] = useState<IDrivers | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [defaultvalue, setDefaultvalue] = useState<any>();
  const [isAbsenceModalOpen, setIsAbsenceModalOpen] = useState(false);

  const { enqueueSnackbar } = useSnackbar();
  const { mutate: deleteDriverMutate } = useDeleteDrivers();
  const { mutate: addDriverMutate } = useAddDrivers();
  const { mutate: editDriverMutate } = useEditDrivers();
  const recordAbsence = useRecordDriverAbsence();

  useEffect(() => {
    if (drivers) {
      const driverWithSr = drivers.map((driverItems, index) => ({
        ...driverItems,
        sr: index + 1,
      }));
      setLocalDriver(driverWithSr);
    }
  }, [drivers]);

  const handleDetails = (rowData: any) => {
    if (rowData) {
      setDriver(rowData);
      setDrawerOpen(true);
    }
  };

  const handleEdit = (rowData: any) => {
    setDriver(rowData);
    setDefaultvalue({
      name: rowData?.name,
      role: rowData?.role,
      rphone: rowData?.rphone,
      mphone: rowData?.mphone,
      jdate: rowData?.jdate,
      address: rowData?.address,
      remark: rowData?.remark,
    });
    setEditDialogOpen(true);
  };

  const handleDelete = (rowData: any) => {
    setDriver(rowData);
    setDeleteDialogOpen(true);
  };

  const handleCloseEditDialog = (action: any) => {
    if (driver && action && action?.action) {
      editDriverMutate(
        { ...action?.formData, id: driver.id },
        {
          onSuccess: () => {
            refetch();
            setEditDialogOpen(false);
            enqueueSnackbar('Content Updated', { variant: 'info' });
          },
          onError: () => {
            enqueueSnackbar('Failed to Update content', { variant: 'error' });
          },
        }
      );
    } else {
      setEditDialogOpen(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  const handleConfirmDelete = () => {
    if (driver) {
      deleteDriverMutate(
        { id: +driver.id },
        {
          onSuccess: () => {
            setLocalDriver(prevDriver =>
              prevDriver.filter(c => c.id !== driver.id)
            );
            enqueueSnackbar('Content Deleted', { variant: 'success' });
          },
          onError: () => {
            enqueueSnackbar('Failed to Delete content', { variant: 'error' });
          },
        }
      );
    }
    setDeleteDialogOpen(false);
  };

  const handleAddDialogOpen = () => {
    setAddDialogOpen(true);
  };

  const handleAddDialogClose = (action: any) => {
    if (action && action?.action) {
      addDriverMutate(action?.formData, {
        onSuccess: () => {
          refetch();
          setAddDialogOpen(false);
        },
        onError: () => {
          enqueueSnackbar('Failed to Add content', { variant: 'error' });
        },
      });
    } else {
      setAddDialogOpen(false);
    }
  };

  const handleAbsenceModalOpen = () => {
    setIsAbsenceModalOpen(true);
  };

  const handleAbsenceModalClose = (action: any) => {
    if (action?.action === true) {
      recordAbsence.mutate(action.formData, {
        onSuccess: () => {
          setIsAbsenceModalOpen(false);
          enqueueSnackbar('Driver absence recorded successfully', {
            variant: 'success',
          });
          handleAbsenceSuccess();
        },
        onError: (error: any) => {
          const errorMessage =
            error?.response?.data?.messages?.error ||
            'Failed to record driver absence';
          enqueueSnackbar(errorMessage, { variant: 'error' });
        },
      });
    } else {
      setIsAbsenceModalOpen(false);
    }
  };

  const handleAbsenceSuccess = () => {
    refetch();
  };

  const columns: Column<IDrivers>[] = [
    {
      title: 'S.No',
      field: 'sr',
      width: '50px',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        color: '#000000',
        fontSize: '0.75rem',
        fontWeight: 500,
        whiteSpace: 'nowrap',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
      },
      cellStyle: {
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        padding: '8px',
        fontSize: '0.75rem',
      },
    },
    {
      title: 'Name',
      field: 'name',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        color: '#000000',
        fontSize: '0.75rem',
        fontWeight: 500,
        whiteSpace: 'nowrap',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
      },
      cellStyle: {
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        padding: '8px',
        fontSize: '0.75rem',
      },
    },
    {
      title: 'Mobile No.',
      field: 'mphone',
      render: rowData => rowData.mphone || 'N/A',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        color: '#000000',
        fontSize: '0.75rem',
        fontWeight: 500,
        whiteSpace: 'nowrap',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
      },
      cellStyle: {
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        padding: '8px',
        fontSize: '0.75rem',
      },
    },
    {
      title: 'Phone No.',
      field: 'rphone',
      render: rowData => rowData.rphone || 'N/A',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        color: '#000000',
        fontSize: '0.75rem',
        fontWeight: 500,
        whiteSpace: 'nowrap',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
      },
      cellStyle: {
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        padding: '8px',
        fontSize: '0.75rem',
      },
    },
    {
      title: 'Position',
      field: 'role',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        color: '#000000',
        fontSize: '0.75rem',
        fontWeight: 500,
        whiteSpace: 'nowrap',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
      },
      cellStyle: {
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        padding: '8px',
        fontSize: '0.75rem',
      },
    },
  ];

  return (
    <Grid
      container
      direction="column"
      spacing={2}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '1rem', sm: '1.5rem' },
      }}
    >
      <Grid item>
        <PageTitle
          text="Drivers/Cleaners"
          primaryAction={
            <div style={{ display: 'flex', gap: '16px' }}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleAbsenceModalOpen}
                startIcon={<PersonOffIcon />}
                sx={{
                  textTransform: 'none',
                  fontWeight: 500,
                  boxShadow: 2,
                  '&:hover': {
                    boxShadow: 4,
                  },
                }}
              >
                Record Absence
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={handleAddDialogOpen}
                startIcon={<AddIcon />}
                sx={{
                  textTransform: 'none',
                  fontWeight: 500,
                  boxShadow: 2,
                  '&:hover': {
                    boxShadow: 4,
                  },
                }}
              >
                Add Drivers/Cleaners
              </Button>
            </div>
          }
        />
      </Grid>

      <Grid item xs container>
        <Paper
          elevation={2}
          sx={{
            width: '100%',
            borderRadius: '8px',
            overflow: 'hidden',
          }}
        >
          <Grid item xs={12}>
            <MaterialTable
              title=""
              columns={columns}
              data={localdriver || []}
              isLoading={isLoading}
              options={{
                draggable: false,
                paging: true,
                pageSize: 20,
                pageSizeOptions: [10, 20, 30, 50],
                search: true,
                tableLayout: 'fixed',
                searchFieldVariant: 'outlined',
                actionsColumnIndex: -1,
                headerStyle: {
                  backgroundColor: '#f5f5f5',
                  color: '#000000',
                  fontSize: '0.75rem',
                  fontWeight: 500,
                },
                rowStyle: {
                  backgroundColor: '#ffffff',
                },
                searchFieldStyle: {
                  marginLeft: 'auto',
                  marginRight: '16px',
                },
              }}
              style={{
                minHeight: 'calc(100vh - 180px)',
                overflowY: 'auto',
              }}
              components={{
                Container: props => (
                  <Paper
                    elevation={0}
                    {...props}
                    sx={{
                      borderRadius: '8px',
                      padding: '16px',
                    }}
                  />
                ),
              }}
              onRowClick={(_, rowData) => handleDetails(rowData)}
              actions={[
                {
                  icon: () => <EditIcon />,
                  tooltip: 'Edit',
                  position: 'row',
                  onClick: (_, rowData) => handleEdit(rowData),
                },
                {
                  icon: () => <DeleteIcon />,
                  tooltip: 'Delete',
                  position: 'row',
                  onClick: (_, rowData) => handleDelete(rowData),
                },
              ]}
            />
          </Grid>
        </Paper>
      </Grid>

      <AddDialog
        open={addDialogOpen}
        handleClose={handleAddDialogClose}
        ContentComponent={AddDriver}
      />

      <DetailDialog
        open={drawerOpen}
        handleCloseDrawer={() => setDrawerOpen(false)}
        ContentComponent={DriverDetail}
        data={driver}
      />

      <EditDialog
        open={editDialogOpen}
        handleClose={handleCloseEditDialog}
        ContentComponent={EditDriver}
        billMade={false}
        defaultvalue={defaultvalue}
      />

      <DeleteDialog
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        handleConfirmDelete={handleConfirmDelete}
      />

      <AbsenceModal
        open={isAbsenceModalOpen}
        onClose={() => handleAbsenceModalClose(null)}
        onSuccess={handleAbsenceSuccess}
      />
    </Grid>
  );
};

export default Drivers;
