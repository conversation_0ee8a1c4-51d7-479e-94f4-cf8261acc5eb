import { useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';

import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { Button, Grid, Paper } from '@mui/material';

import MaterialTable, { Column } from '@material-table/core';

import AddNewCompanies from '@components/ControlPanel/NewCompanies/AddNewCompanies';
// import EditCompanyTariff from '@components/ControlPanel/NewCompanies/CompanyTariff/EditCompanyTariff';
import EditNewCompanies from '@components/ControlPanel/NewCompanies/EditNewCompanies';
import AddDialog from '@components/Dialog/AddDialog';
import DeleteDialog from '@components/Dialog/DeleteDialog';
import EditDialog from '@components/Dialog/EditDialog';
import PageTitle from '@components/PageTitle';

import {
  useAddNCompany,
  useDeleteNCompany,
  useEditNCompany,
  useReadNCompanys,
} from '../../../hooks/controlpanel/newcompanies';
import { INCompany } from '../../../types/controlpanel/newcompanies';

const NewCompanies = () => {
  const { data: ncompanys, isLoading, refetch } = useReadNCompanys();

  const [localncompany, setLocalNcompany] = useState<INCompany[]>([]);
  const [ncompany, setNCompany] = useState<INCompany | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  // const [editTariffDialogOpen, setEditTariffDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [defaultvalue, setDefaultvalue] = useState<any>();
  const [addDialogOpen, setAddDialogOpen] = useState(false);

  const { enqueueSnackbar } = useSnackbar();
  const { mutate: deleteNcompanyMutate } = useDeleteNCompany();
  const { mutate: addNcompanyMutate } = useAddNCompany();
  const { mutate: editNcompanyMutate } = useEditNCompany();

  useEffect(() => {
    if (ncompanys) {
      const ncompanysWithSr = ncompanys.map((ncompanysItems, index) => ({
        ...ncompanysItems,
        sr: index + 1,
      }));
      setLocalNcompany(ncompanysWithSr);
    }
  }, [ncompanys]);

  const handleEdit = (rowData: any) => {
    setNCompany(rowData);
    setDefaultvalue({
      type: rowData?.taxtype,
      info: rowData?.info,
      cname: rowData?.cname,
      tname: rowData?.tname,
      address: rowData?.address,
      edate: rowData?.edate,
      edate1: rowData?.edate1,
      gst: rowData?.gst,
      hrkm: rowData?.hrkm,
      gstdate: rowData?.gstdate,
      gstdate1: rowData?.gstdate1,
      tax: rowData?.tax,
      ntax: rowData?.ntax,
      gstno: rowData?.gstno,
      taxtype: rowData?.taxtype,
      active: rowData?.active,
      gsttype: rowData?.gsttype,
      mintype: rowData?.mintype,
      name: rowData?.name,
      panno: rowData?.panno,
      status: rowData?.status,
      servicetaxtype: rowData?.servicetaxtype,
    });
    setEditDialogOpen(true);
  };

  const handleDelete = (rowData: any) => {
    setNCompany(rowData);
    setDeleteDialogOpen(true);
  };

  const handleCloseEditDialog = (action: any) => {
    if (ncompany && action && action?.action) {
      editNcompanyMutate(
        { ...action?.formData, id: ncompany.id },
        {
          onSuccess: () => {
            refetch();
            setEditDialogOpen(false);
            enqueueSnackbar('Content Updated', { variant: 'info' });
          },
          onError: () => {
            enqueueSnackbar('Failed to Update Content', { variant: 'error' });
          },
        }
      );
    } else {
      setEditDialogOpen(false);
    }
  };

  // const handleCloseEditTariffDialog = (action: any) => {
  //   if (ncompany && action && action?.action) {
  //     editNcompanyMutate(
  //       { ...action?.formData, id: ncompany.id },
  //       {
  //         onSuccess: data => {
  //           const index = localncompany.findIndex(d => d.id === ncompany.id);
  //           if (index >= 0) {
  //             const arr = [...localncompany];
  //             arr[index] = JSON.parse(data?.config?.data);
  //             setLocalNcompany(arr);
  //           }
  //           setEditTariffDialogOpen(false);
  //         },
  //         onError: error => {},
  //       }
  //     );
  //   } else {
  //     setEditTariffDialogOpen(false);
  //   }
  // };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  const handleConfirmDelete = () => {
    if (ncompany) {
      deleteNcompanyMutate(
        { id: +ncompany.id },
        {
          onSuccess: () => {
            setLocalNcompany(prevNcompany =>
              prevNcompany.filter(c => c.id !== ncompany.id)
            );
            enqueueSnackbar('Content Deleted', { variant: 'success' });
          },
          onError: () => {
            enqueueSnackbar('Failed to Delete Content', { variant: 'error' });
          },
        }
      );
    }
    setDeleteDialogOpen(false);
  };

  const handleAddDialogOpen = () => {
    setAddDialogOpen(true);
  };

  const handleAddDialogClose = (action: any) => {
    if (action && action?.action) {
      addNcompanyMutate(action?.formData, {
        onSuccess: () => {
          refetch();
          setAddDialogOpen(false);
        },
        onError: () => {
          enqueueSnackbar('Failed to Add Content', { variant: 'error' });
        },
      });
    } else {
      setAddDialogOpen(false);
    }
  };

  const columns: Column<INCompany>[] = [
    {
      title: 'S.No',
      field: 'sr',
      sorting: true,
      width: '5%',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
        padding: '0px',
        textAlign: 'center',
        fontSize: '0.75rem',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
        paddingLeft: '12px',
      },
      cellStyle: {
        padding: '0px',
        fontSize: '0.7rem',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
    },
    {
      title: 'Name',
      field: 'name',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
        padding: '0px',
        textAlign: 'center',
        fontSize: '0.75rem',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
      cellStyle: {
        padding: '0px',
        fontSize: '0.7rem',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
    },
    {
      title: 'Info',
      field: 'info',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
        padding: '0px',
        textAlign: 'center',
        fontSize: '0.75rem',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
      cellStyle: {
        padding: '0px',
        fontSize: '0.7rem',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
    },
    {
      title: 'Service Tax Type',
      field: 'servicetaxtype',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
        padding: '0px',
        textAlign: 'center',
        fontSize: '0.75rem',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
      cellStyle: {
        padding: '0px',
        fontSize: '0.7rem',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
    },
  ];

  return (
    <Grid
      container
      direction="column"
      spacing={1}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '0.5rem', sm: '1rem' },
      }}
    >
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle
          text="Companies"
          primaryAction={
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddDialogOpen}
            >
              <AddIcon sx={{ mr: 1 }} />
              Add New Company
            </Button>
          }
        />
      </Grid>

      <Grid item xs container spacing={2}>
        <Paper elevation={2} sx={{ width: '100%' }}>
          <Grid item xs={12}>
            <MaterialTable
              title=""
              columns={columns}
              data={localncompany || []}
              isLoading={isLoading}
              options={{
                draggable: false,
                paging: true,
                pageSize: 20,
                pageSizeOptions: [10, 20, 30],
                search: true,
                tableLayout: 'fixed',
                searchFieldVariant: 'outlined',
                actionsColumnIndex: -1,
                headerStyle: {
                  backgroundColor: '#f5f5f5',
                  fontWeight: 'bold',
                  whiteSpace: 'nowrap',
                  padding: '0px',
                  textAlign: 'center',
                  fontSize: '0.75rem',
                  borderRight: '1px solid #ccc',
                },
                rowStyle: {
                  backgroundColor: '#ffffff',
                },
              }}
              style={{
                minHeight: 'calc(100vh - 180px)',
                overflowY: 'auto',
              }}
              components={{
                Container: props => <Paper elevation={0} {...props} />,
              }}
              actions={[
                {
                  icon: EditIcon,
                  tooltip: 'Edit',
                  onClick: (_, rowData) => handleEdit(rowData),
                },
                {
                  icon: DeleteIcon,
                  tooltip: 'Delete',
                  onClick: (_, rowData) => handleDelete(rowData),
                },
              ]}
            />
          </Grid>
        </Paper>
      </Grid>

      <AddDialog
        open={addDialogOpen}
        handleClose={handleAddDialogClose}
        ContentComponent={AddNewCompanies}
      />

      <EditDialog
        open={editDialogOpen}
        handleClose={handleCloseEditDialog}
        ContentComponent={EditNewCompanies}
        billMade={false}
        defaultvalue={defaultvalue}
      />

      {/* <EditDialog
        open={editTariffDialogOpen}
        handleClose={handleCloseEditTariffDialog}
        ContentComponent={EditCompanyTariff}
        billMade={false}
        defaultvalue={defaultvalue}
      /> */}

      <DeleteDialog
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        handleConfirmDelete={handleConfirmDelete}
      />
    </Grid>
  );
};

export default NewCompanies;
