import { useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';

import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { Button, Grid, Paper } from '@mui/material';

import MaterialTable, { Column } from '@material-table/core';

import AddVehicleType from '@components/ControlPanel/VehicleType/AddVehicleType';
import EditVehicleType from '@components/ControlPanel/VehicleType/EditVehicleType';
import AddDialog from '@components/Dialog/AddDialog';
import DeleteDialog from '@components/Dialog/DeleteDialog';
import EditDialog from '@components/Dialog/EditDialog';
import PageTitle from '@components/PageTitle';

import {
  useAddVehicleType,
  useDeleteVehicleType,
  useEditVehicleType,
  useReadVehicleTypes,
} from '../../../hooks/controlpanel/vehicletype';
import { IVehicleType } from '../../../types/controlpanel/vehicletype';

const VehicleType = () => {
  const { data: vtypes, isLoading, refetch } = useReadVehicleTypes();

  const [localvtype, setLocalVtype] = useState<IVehicleType[]>([]);
  const [vtype, setVtype] = useState<IVehicleType | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [defaultvalue, setDefaultvalue] = useState<any>();
  const [addDialogOpen, setAddDialogOpen] = useState(false);

  const { enqueueSnackbar } = useSnackbar();
  const { mutate: deleteVehicletypeMutate } = useDeleteVehicleType();
  const { mutate: addVehicletypeMutate } = useAddVehicleType();
  const { mutate: editVehicletypeMutate } = useEditVehicleType();

  useEffect(() => {
    if (vtypes) {
      const vtypeWithSr = vtypes.map((ctypeItems, index) => ({
        ...ctypeItems,
        sr: index + 1,
      }));
      setLocalVtype(vtypeWithSr);
    }
  }, [vtypes]);

  const handleEdit = (rowData: any) => {
    setVtype(rowData);
    setDefaultvalue({
      type: rowData?.type,
      info: rowData?.info,
    });
    setEditDialogOpen(true);
  };

  const handleDelete = (rowData: any) => {
    setVtype(rowData);
    setDeleteDialogOpen(true);
  };

  const handleCloseEditDialog = (action: any) => {
    if (vtype && action && action?.action) {
      editVehicletypeMutate(
        {
          ...action?.formData,
          id: vtype.id,
        },
        {
          onSuccess: () => {
            refetch();
            setEditDialogOpen(false);
            enqueueSnackbar('Content Updated', { variant: 'info' });
          },
          onError: () => {
            enqueueSnackbar('Failed to Update content', { variant: 'error' });
          },
        }
      );
    } else {
      setEditDialogOpen(false);
    }
  };
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  const handleConfirmDelete = () => {
    if (vtype) {
      deleteVehicletypeMutate(
        { id: +vtype.id },
        {
          onSuccess: () => {
            setLocalVtype(prevVtype =>
              prevVtype.filter(c => c.id !== vtype.id)
            );
            enqueueSnackbar('Content Deleted', { variant: 'success' });
          },
          onError: () => {
            enqueueSnackbar('Failed to Delete Content', { variant: 'error' });
          },
        }
      );
    }
    setDeleteDialogOpen(false);
  };

  const handleAddDialogOpen = () => {
    setAddDialogOpen(true);
  };

  const handleAddDialogClose = (action: any) => {
    if (action && action?.action) {
      addVehicletypeMutate(action?.formData, {
        onSuccess: () => {
          refetch();
          setAddDialogOpen(false);
          enqueueSnackbar('Vehicle Type Added Successfully', {
            variant: 'success',
          });
        },
        onError: (error: any) => {
          if (error.message === 'Vehicle type already exists') {
            enqueueSnackbar('Vehicle Type already exists', {
              variant: 'warning',
            });
          } else {
            enqueueSnackbar('Failed to Add Vehicle Type', {
              variant: 'error',
            });
          }
        },
      });
    } else {
      setAddDialogOpen(false);
    }
  };

  const columns: Column<IVehicleType>[] = [
    {
      title: 'S.No',
      field: 'sr',
      sorting: true,
      width: '5%',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
        paddingLeft: '12px',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Type',
      field: 'type',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
      },
    },
    {
      title: 'Info',
      field: 'info',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
      },
      cellStyle: {
        whiteSpace: 'nowrap',
      },
    },
  ];

  return (
    <Grid
      container
      direction="column"
      spacing={1}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '0.5rem', sm: '1rem' },
      }}
    >
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle
          text="Vehicle Type"
          primaryAction={
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddDialogOpen}
            >
              <AddIcon sx={{ mr: 1 }} />
              Add Vehicle Type
            </Button>
          }
        />
      </Grid>

      <Grid item xs container spacing={2}>
        <Paper elevation={2} sx={{ width: '100%' }}>
          <Grid item xs={12}>
            <MaterialTable
              title=""
              columns={columns}
              data={localvtype || []}
              isLoading={isLoading}
              options={{
                draggable: false,
                paging: false,
                search: true,
                tableLayout: 'fixed',
                searchFieldVariant: 'outlined',
                actionsColumnIndex: -1,
              }}
              style={{
                minHeight: 'calc(100vh - 180px)',
                overflowY: 'auto',
              }}
              components={{
                Container: props => <Paper elevation={0} {...props} />,
              }}
              actions={[
                {
                  icon: EditIcon,
                  tooltip: 'Edit Data',
                  onClick: (_, rowData) => handleEdit(rowData),
                },
                {
                  icon: DeleteIcon,
                  tooltip: 'Delete Data',
                  onClick: (_, rowData) => handleDelete(rowData),
                },
              ]}
            />
          </Grid>
        </Paper>
      </Grid>

      <AddDialog
        open={addDialogOpen}
        handleClose={handleAddDialogClose}
        ContentComponent={AddVehicleType}
      />

      <EditDialog
        open={editDialogOpen}
        handleClose={handleCloseEditDialog}
        ContentComponent={EditVehicleType}
        billMade={false}
        defaultvalue={defaultvalue}
      />

      <DeleteDialog
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        handleConfirmDelete={handleConfirmDelete}
      />
    </Grid>
  );
};

export default VehicleType;
