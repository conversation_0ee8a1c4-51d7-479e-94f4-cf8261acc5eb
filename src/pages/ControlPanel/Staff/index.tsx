import { useEffect, useState } from 'react';

import { useSnackbar } from 'notistack';

import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import { Button, Grid, Paper } from '@mui/material';

import MaterialTable, { Column } from '@material-table/core';

import AddStaff from '@components/ControlPanel/Staff/AddStaff';
import EditStaff from '@components/ControlPanel/Staff/EditStaff';
import StaffDetail from '@components/ControlPanel/Staff/StaffDetail';
import AddDialog from '@components/Dialog/AddDialog';
import DeleteDialog from '@components/Dialog/DeleteDialog';
import DetailDialog from '@components/Dialog/DetailDialog';
import EditDialog from '@components/Dialog/EditDialog';
import PageTitle from '@components/PageTitle';

import {
  useAddStaff,
  useDeleteStaff,
  useEditStaff,
  useReadStaffs,
} from '../../../hooks/controlpanel/staff';
import { IStaff } from '../../../types/controlpanel/staff';

const StaffList = () => {
  const { data: staffs, isLoading, refetch } = useReadStaffs();

  const [localstaff, setLocalStaff] = useState<IStaff[]>([]);
  const [staff, setStaff] = useState<IStaff | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [defaultvalue, setDefaultvalue] = useState<any>();

  const { enqueueSnackbar } = useSnackbar();
  const { mutate: deleteStaffMutate } = useDeleteStaff();
  const { mutate: addStaffMutate } = useAddStaff();
  const { mutate: editStaffMutate } = useEditStaff();

  useEffect(() => {
    if (staffs) {
      const staffsWithSr = staffs.map((staffItems, index) => ({
        ...staffItems,
        sr: index + 1,
      }));
      setLocalStaff(staffsWithSr);
    }
  }, [staffs]);

  const handleDetails = (rowData: any) => {
    if (rowData) {
      setStaff(rowData);
      setDrawerOpen(true);
    }
  };

  const handleEdit = (rowData: any) => {
    setStaff(rowData);
    setDefaultvalue({
      name: rowData?.name,
      spost: rowData?.role,
      rphone: rowData?.rphone,
      mphone: rowData?.mphone,
      date: rowData?.jdate,
      address: rowData?.address,
      staff_remark: rowData?.staff_remark,
    });
    setEditDialogOpen(true);
  };

  const handleDelete = (rowData: any) => {
    setStaff(rowData);
    setDeleteDialogOpen(true);
  };

  const handleCloseEditDialog = (action: any) => {
    if (staff && action && action?.action) {
      editStaffMutate(
        { ...action?.formData, id: staff.id },
        {
          onSuccess: () => {
            refetch();
            setEditDialogOpen(false);
            enqueueSnackbar('Content Updated', { variant: 'info' });
          },
          onError: () => {
            enqueueSnackbar('Failed to Update Content', { variant: 'error' });
          },
        }
      );
    } else {
      setEditDialogOpen(false);
    }
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  const handleConfirmDelete = () => {
    if (staff) {
      deleteStaffMutate(
        { id: +staff.id },
        {
          onSuccess: () => {
            setLocalStaff(prevStaff =>
              prevStaff.filter(c => c.id !== staff.id)
            );
            enqueueSnackbar('Content Deleted', { variant: 'success' });
          },
          onError: () => {
            enqueueSnackbar('Failed to Delete Content', { variant: 'error' });
          },
        }
      );
    }
    setDeleteDialogOpen(false);
  };

  const handleAddDialogOpen = () => {
    setAddDialogOpen(true);
  };

  const handleAddDialogClose = (action: any) => {
    if (action && action?.action) {
      addStaffMutate(action?.formData, {
        onSuccess: () => {
          refetch();
          setAddDialogOpen(false);
        },
        onError: () => {
          enqueueSnackbar('Failed to Add Content', { variant: 'error' });
        },
      });
    } else {
      setAddDialogOpen(false);
    }
  };

  const columns: Column<IStaff>[] = [
    {
      title: 'S.No',
      field: 'sr',
      sorting: true,
      width: '5%',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
        padding: '0px',
        textAlign: 'center',
        fontSize: '0.75rem',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
        paddingLeft: '12px',
      },
      cellStyle: {
        padding: '0px',
        fontSize: '0.7rem',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
    },
    {
      title: 'Name',
      field: 'name',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
        padding: '0px',
        textAlign: 'center',
        fontSize: '0.75rem',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
      cellStyle: {
        padding: '0px',
        fontSize: '0.7rem',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
    },
    {
      title: 'Mobile No.',
      field: 'mphone',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
        padding: '0px',
        textAlign: 'center',
        fontSize: '0.75rem',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
      cellStyle: {
        padding: '0px',
        fontSize: '0.7rem',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
    },
    {
      title: 'Phone No.',
      field: 'rphone',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
        padding: '0px',
        textAlign: 'center',
        fontSize: '0.75rem',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
      cellStyle: {
        padding: '0px',
        fontSize: '0.7rem',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
    },
    {
      title: 'Position',
      field: 'role',
      headerStyle: {
        backgroundColor: '#f5f5f5',
        fontWeight: 'bold',
        whiteSpace: 'nowrap',
        padding: '0px',
        textAlign: 'center',
        fontSize: '0.75rem',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
      cellStyle: {
        padding: '0px',
        fontSize: '0.7rem',
        textAlign: 'center',
        borderRight: '1px solid #ccc',
        maxWidth: '80px',
      },
    },
  ];

  return (
    <Grid
      container
      direction="column"
      spacing={1}
      wrap="nowrap"
      sx={{
        height: '100%',
        padding: { xs: '0.5rem', sm: '1rem' },
      }}
    >
      <Grid item xs={2} sm={1} sx={{ paddingY: '1rem' }}>
        <PageTitle
          text="Office Staff"
          primaryAction={
            <Button
              variant="contained"
              color="primary"
              onClick={handleAddDialogOpen}
            >
              <AddIcon sx={{ mr: 1 }} />
              Add Staff
            </Button>
          }
        />
      </Grid>

      <Grid item xs container spacing={2}>
        <Paper elevation={2} sx={{ width: '100%' }}>
          <Grid item xs={12}>
            <MaterialTable
              title=""
              columns={columns}
              data={localstaff || []}
              isLoading={isLoading}
              options={{
                draggable: false,
                paging: false,
                search: true,
                tableLayout: 'fixed',
                searchFieldVariant: 'outlined',
                actionsColumnIndex: -1,
              }}
              style={{
                minHeight: 'calc(100vh - 180px)',
                overflowY: 'auto',
              }}
              components={{
                Container: props => <Paper elevation={0} {...props} />,
              }}
              onRowClick={(_, rowData) => handleDetails(rowData)}
              actions={[
                {
                  icon: EditIcon,
                  tooltip: 'Edit',
                  onClick: (_, rowData) => handleEdit(rowData),
                },
                {
                  icon: DeleteIcon,
                  tooltip: 'Delete',
                  onClick: (_, rowData) => handleDelete(rowData),
                },
              ]}
            />
          </Grid>
        </Paper>
      </Grid>

      <AddDialog
        open={addDialogOpen}
        handleClose={handleAddDialogClose}
        ContentComponent={AddStaff}
      />

      <DetailDialog
        open={drawerOpen}
        handleCloseDrawer={() => setDrawerOpen(false)}
        ContentComponent={StaffDetail}
        data={staff}
      />

      <EditDialog
        open={editDialogOpen}
        handleClose={handleCloseEditDialog}
        ContentComponent={EditStaff}
        billMade={false}
        defaultvalue={defaultvalue}
      />

      <DeleteDialog
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        handleConfirmDelete={handleConfirmDelete}
      />
    </Grid>
  );
};

export default StaffList;
