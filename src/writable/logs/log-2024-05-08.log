ERROR - 2024-05-08 04:23:46 --> mysqli_sql_exception: Column 'number' cannot be null in /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:308
Stack trace:
#0 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(308): mysqli->query()
#1 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(722): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(636): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(2325): CodeIgniter\Database\BaseConnection->query()
#4 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Model.php(383): CodeIgniter\Database\BaseBuilder->insert()
#5 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/BaseModel.php(840): CodeIgniter\Model->doInsert()
#6 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Model.php(791): CodeIgniter\BaseModel->insert()
#7 /home/<USER>/Documents/nunes-v4/app/Controllers/VehNo.php(50): CodeIgniter\Model->insert()
#8 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\VehNo->create()
#9 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
#10 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
#11 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#12 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
#13 /home/<USER>/Documents/nunes-v4/public/index.php(56): CodeIgniter\Boot::bootWeb()
#14 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('...')
#15 {main}
CRITICAL - 2024-05-08 04:23:46 --> CodeIgniter\Database\Exceptions\DatabaseException: Column 'number' cannot be null
[Method: POST, Route: vehNo]
in SYSTEMPATH/Database/BaseConnection.php on line 676.
 1 SYSTEMPATH/Database/BaseBuilder.php(2325): CodeIgniter\Database\BaseConnection->query()
 2 SYSTEMPATH/Model.php(383): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH/BaseModel.php(840): CodeIgniter\Model->doInsert()
 4 SYSTEMPATH/Model.php(791): CodeIgniter\BaseModel->insert()
 5 APPPATH/Controllers/VehNo.php(50): CodeIgniter\Model->insert()
 6 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->create()
 7 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 9 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
11 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
12 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/home/<USER>/Documents/nunes-v4/public/index.php')
CRITICAL - 2024-05-08 04:23:46 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Column 'number' cannot be null
in SYSTEMPATH/Database/MySQLi/Connection.php on line 313.
 1 SYSTEMPATH/Database/BaseConnection.php(722): CodeIgniter\Database\MySQLi\Connection->execute()
 2 SYSTEMPATH/Database/BaseConnection.php(636): CodeIgniter\Database\BaseConnection->simpleQuery()
 3 SYSTEMPATH/Database/BaseBuilder.php(2325): CodeIgniter\Database\BaseConnection->query()
 4 SYSTEMPATH/Model.php(383): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH/BaseModel.php(840): CodeIgniter\Model->doInsert()
 6 SYSTEMPATH/Model.php(791): CodeIgniter\BaseModel->insert()
 7 APPPATH/Controllers/VehNo.php(50): CodeIgniter\Model->insert()
 8 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->create()
 9 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
10 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
11 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
13 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
14 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/home/<USER>/Documents/nunes-v4/public/index.php')
CRITICAL - 2024-05-08 04:23:46 --> [Caused by] mysqli_sql_exception: Column 'number' cannot be null
in SYSTEMPATH/Database/MySQLi/Connection.php on line 308.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(308): mysqli->query()
 2 SYSTEMPATH/Database/BaseConnection.php(722): CodeIgniter\Database\MySQLi\Connection->execute()
 3 SYSTEMPATH/Database/BaseConnection.php(636): CodeIgniter\Database\BaseConnection->simpleQuery()
 4 SYSTEMPATH/Database/BaseBuilder.php(2325): CodeIgniter\Database\BaseConnection->query()
 5 SYSTEMPATH/Model.php(383): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH/BaseModel.php(840): CodeIgniter\Model->doInsert()
 7 SYSTEMPATH/Model.php(791): CodeIgniter\BaseModel->insert()
 8 APPPATH/Controllers/VehNo.php(50): CodeIgniter\Model->insert()
 9 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->create()
10 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
11 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
12 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
14 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
15 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/home/<USER>/Documents/nunes-v4/public/index.php')
ERROR - 2024-05-08 04:27:43 --> mysqli_sql_exception: Column 'number' cannot be null in /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:308
Stack trace:
#0 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(308): mysqli->query()
#1 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(722): CodeIgniter\Database\MySQLi\Connection->execute()
#2 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(636): CodeIgniter\Database\BaseConnection->simpleQuery()
#3 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(2325): CodeIgniter\Database\BaseConnection->query()
#4 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Model.php(383): CodeIgniter\Database\BaseBuilder->insert()
#5 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/BaseModel.php(840): CodeIgniter\Model->doInsert()
#6 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Model.php(791): CodeIgniter\BaseModel->insert()
#7 /home/<USER>/Documents/nunes-v4/app/Controllers/VehNo.php(50): CodeIgniter\Model->insert()
#8 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\VehNo->create()
#9 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
#10 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
#11 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#12 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
#13 /home/<USER>/Documents/nunes-v4/public/index.php(56): CodeIgniter\Boot::bootWeb()
#14 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('...')
#15 {main}
CRITICAL - 2024-05-08 04:27:43 --> CodeIgniter\Database\Exceptions\DatabaseException: Column 'number' cannot be null
[Method: POST, Route: vehno]
in SYSTEMPATH/Database/BaseConnection.php on line 676.
 1 SYSTEMPATH/Database/BaseBuilder.php(2325): CodeIgniter\Database\BaseConnection->query()
 2 SYSTEMPATH/Model.php(383): CodeIgniter\Database\BaseBuilder->insert()
 3 SYSTEMPATH/BaseModel.php(840): CodeIgniter\Model->doInsert()
 4 SYSTEMPATH/Model.php(791): CodeIgniter\BaseModel->insert()
 5 APPPATH/Controllers/VehNo.php(50): CodeIgniter\Model->insert()
 6 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->create()
 7 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
 8 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 9 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
11 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
12 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/home/<USER>/Documents/nunes-v4/public/index.php')
CRITICAL - 2024-05-08 04:27:43 --> [Caused by] CodeIgniter\Database\Exceptions\DatabaseException: Column 'number' cannot be null
in SYSTEMPATH/Database/MySQLi/Connection.php on line 313.
 1 SYSTEMPATH/Database/BaseConnection.php(722): CodeIgniter\Database\MySQLi\Connection->execute()
 2 SYSTEMPATH/Database/BaseConnection.php(636): CodeIgniter\Database\BaseConnection->simpleQuery()
 3 SYSTEMPATH/Database/BaseBuilder.php(2325): CodeIgniter\Database\BaseConnection->query()
 4 SYSTEMPATH/Model.php(383): CodeIgniter\Database\BaseBuilder->insert()
 5 SYSTEMPATH/BaseModel.php(840): CodeIgniter\Model->doInsert()
 6 SYSTEMPATH/Model.php(791): CodeIgniter\BaseModel->insert()
 7 APPPATH/Controllers/VehNo.php(50): CodeIgniter\Model->insert()
 8 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->create()
 9 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
10 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
11 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
12 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
13 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
14 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/home/<USER>/Documents/nunes-v4/public/index.php')
CRITICAL - 2024-05-08 04:27:43 --> [Caused by] mysqli_sql_exception: Column 'number' cannot be null
in SYSTEMPATH/Database/MySQLi/Connection.php on line 308.
 1 SYSTEMPATH/Database/MySQLi/Connection.php(308): mysqli->query()
 2 SYSTEMPATH/Database/BaseConnection.php(722): CodeIgniter\Database\MySQLi\Connection->execute()
 3 SYSTEMPATH/Database/BaseConnection.php(636): CodeIgniter\Database\BaseConnection->simpleQuery()
 4 SYSTEMPATH/Database/BaseBuilder.php(2325): CodeIgniter\Database\BaseConnection->query()
 5 SYSTEMPATH/Model.php(383): CodeIgniter\Database\BaseBuilder->insert()
 6 SYSTEMPATH/BaseModel.php(840): CodeIgniter\Model->doInsert()
 7 SYSTEMPATH/Model.php(791): CodeIgniter\BaseModel->insert()
 8 APPPATH/Controllers/VehNo.php(50): CodeIgniter\Model->insert()
 9 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->create()
10 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
11 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
12 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
13 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
14 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
15 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/home/<USER>/Documents/nunes-v4/public/index.php')
CRITICAL - 2024-05-08 05:26:46 --> ErrorException: Undefined array key "num"
[Method: POST, Route: vehno]
in APPPATH/Controllers/VehNo.php on line 46.
 1 APPPATH/Controllers/VehNo.php(46): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->create()
 3 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 5 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 7 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
 8 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/home/<USER>/Documents/nunes-v4/public/index.php')
CRITICAL - 2024-05-08 05:27:09 --> ErrorException: Undefined array key "num"
[Method: POST, Route: vehno]
in APPPATH/Controllers/VehNo.php on line 46.
 1 APPPATH/Controllers/VehNo.php(46): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->create()
 3 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 5 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 7 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
 8 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/home/<USER>/Documents/nunes-v4/public/index.php')
CRITICAL - 2024-05-08 05:29:58 --> ErrorException: Undefined array key "num"
[Method: POST, Route: vehno]
in APPPATH/Controllers/VehNo.php on line 47.
 1 APPPATH/Controllers/VehNo.php(47): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->create()
 3 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 5 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 7 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
 8 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/home/<USER>/Documents/nunes-v4/public/index.php')
CRITICAL - 2024-05-08 05:33:59 --> Error: Cannot use object of type stdClass as array
[Method: POST, Route: vehno]
in APPPATH/Controllers/VehNo.php on line 47.
 1 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->create()
 2 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 4 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 6 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
 7 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/home/<USER>/Documents/nunes-v4/public/index.php')
CRITICAL - 2024-05-08 05:36:34 --> Error: Cannot use object of type stdClass as array
[Method: POST, Route: vehno]
in APPPATH/Controllers/VehNo.php on line 45.
 1 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->create()
 2 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
 3 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 4 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
 5 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 6 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
 7 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/home/<USER>/Documents/nunes-v4/public/index.php')
CRITICAL - 2024-05-08 05:41:14 --> ErrorException: Undefined property: stdClass::$name
[Method: POST, Route: vehno]
in APPPATH/Controllers/VehNo.php on line 45.
 1 APPPATH/Controllers/VehNo.php(45): CodeIgniter\Debug\Exceptions->errorHandler()
 2 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->create()
 3 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController()
 4 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest()
 5 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
 6 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter()
 7 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb()
 8 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/home/<USER>/Documents/nunes-v4/public/index.php')
