ERROR - 2024-05-09 06:42:44 --> Error connecting to the database: mysqli_sql_exception: Connection refused in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:187
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(187): mysqli->real_connect('127.0.0.1', 'root', 'mysql', 'nunes', 3310, '', 0)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#3 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 /var/www/html/vendor/codeigniter4/framework/system/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
#5 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
#6 /var/www/html/app/Controllers/Project.php(19): CodeIgniter\BaseModel->findAll()
#7 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\Project->index()
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Project))
#9 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#11 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 /var/www/html/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 /var/www/html/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('/var/www/html/p...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Connection refused in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:231
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 /var/www/html/vendor/codeigniter4/framework/system/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
#4 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
#5 /var/www/html/app/Controllers/Project.php(19): CodeIgniter\BaseModel->findAll()
#6 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\Project->index()
#7 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Project))
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#10 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 /var/www/html/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 /var/www/html/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('/var/www/html/p...')
#13 {main}
CRITICAL - 2024-05-09 06:42:44 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Connection refused
[Method: GET, Route: projects]
in SYSTEMPATH/Database/BaseConnection.php on line 457.
 1 SYSTEMPATH/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `projects`
WHERE `projects`.`deleted_at` IS NULL', [], false)
 3 SYSTEMPATH/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH/Controllers/Project.php(19): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Project->index()
 7 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Project))
 8 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/var/www/html/public/index.php')
ERROR - 2024-05-09 06:42:55 --> Error connecting to the database: mysqli_sql_exception: Connection refused in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:187
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(187): mysqli->real_connect('127.0.0.1', 'root', 'mysql', 'nunes', 3310, '', 0)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#3 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 /var/www/html/vendor/codeigniter4/framework/system/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
#5 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
#6 /var/www/html/app/Controllers/VehNo.php(19): CodeIgniter\BaseModel->findAll()
#7 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\VehNo->index()
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\VehNo))
#9 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#11 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 /var/www/html/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 /var/www/html/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('/var/www/html/p...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Connection refused in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:231
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 /var/www/html/vendor/codeigniter4/framework/system/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
#4 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
#5 /var/www/html/app/Controllers/VehNo.php(19): CodeIgniter\BaseModel->findAll()
#6 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\VehNo->index()
#7 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\VehNo))
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#10 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 /var/www/html/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 /var/www/html/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('/var/www/html/p...')
#13 {main}
CRITICAL - 2024-05-09 06:42:55 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Connection refused
[Method: GET, Route: vehno]
in SYSTEMPATH/Database/BaseConnection.php on line 457.
 1 SYSTEMPATH/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `vehno`', [], false)
 3 SYSTEMPATH/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH/Controllers/VehNo.php(19): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->index()
 7 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\VehNo))
 8 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/var/www/html/public/index.php')
ERROR - 2024-05-09 06:50:24 --> Error connecting to the database: mysqli_sql_exception: Connection refused in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:187
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(187): mysqli->real_connect('127.0.0.1', 'root', 'mysql', 'nunes', 3310, '', 0)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#3 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 /var/www/html/vendor/codeigniter4/framework/system/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
#5 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
#6 /var/www/html/app/Controllers/Project.php(19): CodeIgniter\BaseModel->findAll()
#7 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\Project->index()
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Project))
#9 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#11 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 /var/www/html/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 /var/www/html/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('/var/www/html/p...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Connection refused in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:231
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 /var/www/html/vendor/codeigniter4/framework/system/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
#4 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
#5 /var/www/html/app/Controllers/Project.php(19): CodeIgniter\BaseModel->findAll()
#6 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\Project->index()
#7 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Project))
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#10 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 /var/www/html/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 /var/www/html/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('/var/www/html/p...')
#13 {main}
CRITICAL - 2024-05-09 06:50:24 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Connection refused
[Method: GET, Route: projects]
in SYSTEMPATH/Database/BaseConnection.php on line 457.
 1 SYSTEMPATH/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `projects`
WHERE `projects`.`deleted_at` IS NULL', [], false)
 3 SYSTEMPATH/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH/Controllers/Project.php(19): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Project->index()
 7 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Project))
 8 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/var/www/html/public/index.php')
ERROR - 2024-05-09 06:50:28 --> Error connecting to the database: mysqli_sql_exception: Connection refused in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:187
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(187): mysqli->real_connect('127.0.0.1', 'root', 'mysql', 'nunes', 3310, '', 0)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#3 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 /var/www/html/vendor/codeigniter4/framework/system/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
#5 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
#6 /var/www/html/app/Controllers/Project.php(19): CodeIgniter\BaseModel->findAll()
#7 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\Project->index()
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Project))
#9 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#11 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 /var/www/html/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 /var/www/html/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('/var/www/html/p...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Connection refused in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:231
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 /var/www/html/vendor/codeigniter4/framework/system/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
#4 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
#5 /var/www/html/app/Controllers/Project.php(19): CodeIgniter\BaseModel->findAll()
#6 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\Project->index()
#7 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Project))
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#10 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 /var/www/html/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 /var/www/html/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('/var/www/html/p...')
#13 {main}
CRITICAL - 2024-05-09 06:50:28 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Connection refused
[Method: GET, Route: project]
in SYSTEMPATH/Database/BaseConnection.php on line 457.
 1 SYSTEMPATH/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `projects`
WHERE `projects`.`deleted_at` IS NULL', [], false)
 3 SYSTEMPATH/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH/Controllers/Project.php(19): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Project->index()
 7 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Project))
 8 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/var/www/html/public/index.php')
ERROR - 2024-05-09 06:50:30 --> Error connecting to the database: mysqli_sql_exception: Connection refused in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:187
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(187): mysqli->real_connect('127.0.0.1', 'root', 'mysql', 'nunes', 3310, '', 0)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#3 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 /var/www/html/vendor/codeigniter4/framework/system/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
#5 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
#6 /var/www/html/app/Controllers/Project.php(19): CodeIgniter\BaseModel->findAll()
#7 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\Project->index()
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Project))
#9 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#11 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 /var/www/html/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 /var/www/html/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('/var/www/html/p...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Connection refused in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:231
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 /var/www/html/vendor/codeigniter4/framework/system/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
#4 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
#5 /var/www/html/app/Controllers/Project.php(19): CodeIgniter\BaseModel->findAll()
#6 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\Project->index()
#7 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Project))
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#10 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 /var/www/html/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 /var/www/html/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('/var/www/html/p...')
#13 {main}
CRITICAL - 2024-05-09 06:50:30 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Connection refused
[Method: GET, Route: projects]
in SYSTEMPATH/Database/BaseConnection.php on line 457.
 1 SYSTEMPATH/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `projects`
WHERE `projects`.`deleted_at` IS NULL', [], false)
 3 SYSTEMPATH/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH/Controllers/Project.php(19): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\Project->index()
 7 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\Project))
 8 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/var/www/html/public/index.php')
ERROR - 2024-05-09 06:50:35 --> Error connecting to the database: mysqli_sql_exception: Connection refused in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:187
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(187): mysqli->real_connect('127.0.0.1', 'root', 'mysql', 'nunes', 3310, '', 0)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#3 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#4 /var/www/html/vendor/codeigniter4/framework/system/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
#5 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
#6 /var/www/html/app/Controllers/VehNo.php(19): CodeIgniter\BaseModel->findAll()
#7 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\VehNo->index()
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\VehNo))
#9 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#11 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 /var/www/html/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 /var/www/html/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('/var/www/html/p...')
#14 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Connection refused in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:231
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *\nFROM `...', Array, false)
#3 /var/www/html/vendor/codeigniter4/framework/system/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
#4 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
#5 /var/www/html/app/Controllers/VehNo.php(19): CodeIgniter\BaseModel->findAll()
#6 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\VehNo->index()
#7 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\VehNo))
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#9 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#10 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#11 /var/www/html/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#12 /var/www/html/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('/var/www/html/p...')
#13 {main}
CRITICAL - 2024-05-09 06:50:35 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Connection refused
[Method: GET, Route: vehno]
in SYSTEMPATH/Database/BaseConnection.php on line 457.
 1 SYSTEMPATH/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH/Database/BaseBuilder.php(1629): CodeIgniter\Database\BaseConnection->query('SELECT *
FROM `vehno`', [], false)
 3 SYSTEMPATH/Model.php(275): CodeIgniter\Database\BaseBuilder->get()
 4 SYSTEMPATH/BaseModel.php(676): CodeIgniter\Model->doFindAll(0, 0)
 5 APPPATH/Controllers/VehNo.php(19): CodeIgniter\BaseModel->findAll()
 6 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->index()
 7 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\VehNo))
 8 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
 9 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
10 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
11 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
12 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/var/www/html/public/index.php')
ERROR - 2024-05-09 06:50:44 --> Error connecting to the database: mysqli_sql_exception: Connection refused in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:187
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(187): mysqli->real_connect('127.0.0.1', 'root', 'mysql', 'nunes', 3310, '', 0)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#3 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(2325): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ve...', Array, false)
#4 /var/www/html/vendor/codeigniter4/framework/system/Model.php(383): CodeIgniter\Database\BaseBuilder->insert()
#5 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(840): CodeIgniter\Model->doInsert(Array)
#6 /var/www/html/vendor/codeigniter4/framework/system/Model.php(791): CodeIgniter\BaseModel->insert(Array, true)
#7 /var/www/html/app/Controllers/VehNo.php(51): CodeIgniter\Model->insert(Array)
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\VehNo->create()
#9 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\VehNo))
#10 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#11 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#12 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#13 /var/www/html/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#14 /var/www/html/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('/var/www/html/p...')
#15 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Connection refused in /var/www/html/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:231
Stack trace:
#0 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect(false)
#1 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#2 /var/www/html/vendor/codeigniter4/framework/system/Database/BaseBuilder.php(2325): CodeIgniter\Database\BaseConnection->query('INSERT INTO `ve...', Array, false)
#3 /var/www/html/vendor/codeigniter4/framework/system/Model.php(383): CodeIgniter\Database\BaseBuilder->insert()
#4 /var/www/html/vendor/codeigniter4/framework/system/BaseModel.php(840): CodeIgniter\Model->doInsert(Array)
#5 /var/www/html/vendor/codeigniter4/framework/system/Model.php(791): CodeIgniter\BaseModel->insert(Array, true)
#6 /var/www/html/app/Controllers/VehNo.php(51): CodeIgniter\Model->insert(Array)
#7 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(933): App\Controllers\VehNo->create()
#8 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\VehNo))
#9 /var/www/html/vendor/codeigniter4/framework/system/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(NULL, Object(Config\Cache), false)
#10 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(312): CodeIgniter\CodeIgniter->run()
#11 /var/www/html/vendor/codeigniter4/framework/system/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
#12 /var/www/html/public/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
#13 /var/www/html/vendor/codeigniter4/framework/system/Commands/Server/rewrite.php(49): require_once('/var/www/html/p...')
#14 {main}
CRITICAL - 2024-05-09 06:50:44 --> CodeIgniter\Database\Exceptions\DatabaseException: Unable to connect to the database.
Main connection [MySQLi]: Connection refused
[Method: POST, Route: vehno]
in SYSTEMPATH/Database/BaseConnection.php on line 457.
 1 SYSTEMPATH/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
 2 SYSTEMPATH/Database/BaseBuilder.php(2325): CodeIgniter\Database\BaseConnection->query('INSERT INTO `vehno` (`number`, `info`) VALUES (:number:, :info:)', [...], false)
 3 SYSTEMPATH/Model.php(383): CodeIgniter\Database\BaseBuilder->insert()
 4 SYSTEMPATH/BaseModel.php(840): CodeIgniter\Model->doInsert([...])
 5 SYSTEMPATH/Model.php(791): CodeIgniter\BaseModel->insert([...], true)
 6 APPPATH/Controllers/VehNo.php(51): CodeIgniter\Model->insert([...])
 7 SYSTEMPATH/CodeIgniter.php(933): App\Controllers\VehNo->create()
 8 SYSTEMPATH/CodeIgniter.php(509): CodeIgniter\CodeIgniter->runController(Object(App\Controllers\VehNo))
 9 SYSTEMPATH/CodeIgniter.php(355): CodeIgniter\CodeIgniter->handleRequest(null, Object(Config\Cache), false)
10 SYSTEMPATH/Boot.php(312): CodeIgniter\CodeIgniter->run()
11 SYSTEMPATH/Boot.php(67): CodeIgniter\Boot::runCodeIgniter(Object(CodeIgniter\CodeIgniter))
12 FCPATH/index.php(56): CodeIgniter\Boot::bootWeb(Object(Config\Paths))
13 SYSTEMPATH/Commands/Server/rewrite.php(49): require_once('/var/www/html/public/index.php')
