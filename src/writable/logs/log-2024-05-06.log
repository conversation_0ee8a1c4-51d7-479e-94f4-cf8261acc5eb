CRITICAL - 2024-05-06 09:53:13 --> Error: Undefined constant "CodeIgniter\Database\MySQLi\MYSQLI_STORE_RESULT"
[Method: CLI, Route: migrate]
in SYSTEMPATH/Database/Database.php on line 139.
 1 SYSTEMPATH/Database/Database.php(57): CodeIgniter\Database\Database->initDriver()
 2 SYSTEMPATH/Database/Config.php(84): CodeIgniter\Database\Database->load()
 3 SYSTEMPATH/Common.php(360): CodeIgniter\Database\Config::connect()
 4 SYSTEMPATH/Database/MigrationRunner.php(146): db_connect()
 5 SYSTEMPATH/Config/Services.php(419): CodeIgniter\Database\MigrationRunner->__construct()
 6 SYSTEMPATH/Config/BaseService.php(311): CodeIgniter\Config\Services::migrations()
 7 SYSTEMPATH/Config/BaseService.php(250): CodeIgniter\Config\BaseService::__callStatic()
 8 SYSTEMPATH/Config/Services.php(414): CodeIgniter\Config\BaseService::getSharedInstance()
 9 SYSTEMPATH/Config/BaseService.php(320): CodeIgniter\Config\Services::migrations()
10 SYSTEMPATH/Config/BaseService.php(201): CodeIgniter\Config\BaseService::__callStatic()
11 SYSTEMPATH/Common.php(998): CodeIgniter\Config\BaseService::get()
12 SYSTEMPATH/Commands/Database/Migrate.php(70): service()
13 SYSTEMPATH/CLI/Commands.php(70): CodeIgniter\Commands\Database\Migrate->run()
14 SYSTEMPATH/CLI/Console.php(48): CodeIgniter\CLI\Commands->run()
15 SYSTEMPATH/Boot.php(338): CodeIgniter\CLI\Console->run()
16 SYSTEMPATH/Boot.php(104): CodeIgniter\Boot::runCommand()
17 ROOTPATH/spark(84): CodeIgniter\Boot::bootSpark()
ERROR - 2024-05-06 09:56:10 --> Error connecting to the database: mysqli_sql_exception: Access denied for user 'root'@'localhost' in /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:187
Stack trace:
#0 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(187): mysqli->real_connect()
#1 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect()
#2 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#3 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1471): CodeIgniter\Database\BaseConnection->query()
#4 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1490): CodeIgniter\Database\BaseConnection->listTables()
#5 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(759): CodeIgniter\Database\BaseConnection->tableExists()
#6 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(163): CodeIgniter\Database\MigrationRunner->ensureTable()
#7 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Commands/Database/Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest()
#8 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/CLI/Commands.php(70): CodeIgniter\Commands\Database\Migrate->run()
#9 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/CLI/Console.php(48): CodeIgniter\CLI\Commands->run()
#10 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Boot.php(338): CodeIgniter\CLI\Console->run()
#11 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Boot.php(104): CodeIgniter\Boot::runCommand()
#12 /home/<USER>/Documents/nunes-v4/spark(84): CodeIgniter\Boot::bootSpark()
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Access denied for user '****'@'localhost' in /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:231
Stack trace:
#0 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect()
#1 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#2 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1471): CodeIgniter\Database\BaseConnection->query()
#3 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1490): CodeIgniter\Database\BaseConnection->listTables()
#4 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(759): CodeIgniter\Database\BaseConnection->tableExists()
#5 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(163): CodeIgniter\Database\MigrationRunner->ensureTable()
#6 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Commands/Database/Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest()
#7 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/CLI/Commands.php(70): CodeIgniter\Commands\Database\Migrate->run()
#8 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/CLI/Console.php(48): CodeIgniter\CLI\Commands->run()
#9 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Boot.php(338): CodeIgniter\CLI\Console->run()
#10 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Boot.php(104): CodeIgniter\Boot::runCommand()
#11 /home/<USER>/Documents/nunes-v4/spark(84): CodeIgniter\Boot::bootSpark()
#12 {main}
ERROR - 2024-05-06 10:13:13 --> Error connecting to the database: mysqli_sql_exception: Connection refused in /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:187
Stack trace:
#0 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php(187): mysqli->real_connect()
#1 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect()
#2 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#3 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1471): CodeIgniter\Database\BaseConnection->query()
#4 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1490): CodeIgniter\Database\BaseConnection->listTables()
#5 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(759): CodeIgniter\Database\BaseConnection->tableExists()
#6 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(163): CodeIgniter\Database\MigrationRunner->ensureTable()
#7 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Commands/Database/Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest()
#8 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/CLI/Commands.php(70): CodeIgniter\Commands\Database\Migrate->run()
#9 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/CLI/Console.php(48): CodeIgniter\CLI\Commands->run()
#10 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Boot.php(338): CodeIgniter\CLI\Console->run()
#11 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Boot.php(104): CodeIgniter\Boot::runCommand()
#12 /home/<USER>/Documents/nunes-v4/spark(84): CodeIgniter\Boot::bootSpark()
#13 {main}

Next CodeIgniter\Database\Exceptions\DatabaseException: Connection refused in /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MySQLi/Connection.php:231
Stack trace:
#0 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(421): CodeIgniter\Database\MySQLi\Connection->connect()
#1 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(604): CodeIgniter\Database\BaseConnection->initialize()
#2 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1471): CodeIgniter\Database\BaseConnection->query()
#3 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/BaseConnection.php(1490): CodeIgniter\Database\BaseConnection->listTables()
#4 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(759): CodeIgniter\Database\BaseConnection->tableExists()
#5 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Database/MigrationRunner.php(163): CodeIgniter\Database\MigrationRunner->ensureTable()
#6 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Commands/Database/Migrate.php(85): CodeIgniter\Database\MigrationRunner->latest()
#7 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/CLI/Commands.php(70): CodeIgniter\Commands\Database\Migrate->run()
#8 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/CLI/Console.php(48): CodeIgniter\CLI\Commands->run()
#9 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Boot.php(338): CodeIgniter\CLI\Console->run()
#10 /home/<USER>/Documents/nunes-v4/vendor/codeigniter4/framework/system/Boot.php(104): CodeIgniter\Boot::runCommand()
#11 /home/<USER>/Documents/nunes-v4/spark(84): CodeIgniter\Boot::bootSpark()
#12 {main}
