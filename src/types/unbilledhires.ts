// export interface IUnbilled {
//   id: number;
//   date: string;
//   company: string;
//   vtype: string;
//   ocode: string;
//   vno: number;
//   particulars: string;
//   htype: string;
//   sagent: number;
//   e: string;
//   extras: number;
// }

export interface IUnbilledHire {
  id: number;
  firstName: string;
  address: {
    address: string;
  };
  phone: string;
}

export interface IReadUnbilledHire {
  id: number;
}
