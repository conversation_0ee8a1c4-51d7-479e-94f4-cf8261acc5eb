export interface IHireChart {
  pdcMark: boolean | undefined;
  id: string;
  date: string;
  company: string;
  ac: string;
  vtype: string;
  ocode: string;
  vno: string;
  repveh: string;
  repdriver: string;
  rvocode: string;
  particulars: string;
  additional_chargs: string;
  additional_amount: string;
  additional_chargs1: string;
  additional_amount1: string;
  additional_chargs2: string;
  additional_amount2: string;
  additional_chargs3: string;
  additional_amount3: string;
  additional_chargs4: string;
  additional_amount4: string;
  hiretype: string;
  client: string;
  subagent: string;
  early: string;
  early_morning: string;
  late: string;
  onite: string;
  toll: string;
  tollc: string;
  billno: string;
  driver: string;
  cleaner: string;
  user: string;
  bill: string;
  remark: string;
  och: string;
  break: string;
  okm: string;
  ckm: string;
  tkm: string;
  akm: string;
  ttkm: string;
  ctid: string;
  checked_transfer: string;
  particulars_type: string;
  proformano: string;
  proforma: string;
  discount: string;
  fullbillno?: string;
}

export interface IReadHireChart {
  id: string;
}
