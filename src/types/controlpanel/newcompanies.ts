export interface INCompany {
  id: string;
  name: string;
  address: string;
  info: string;
  edate: string;
  edate1: string;
  servicetaxtype: string;
  cname: string;
  gstno: string;
  panno: string;
  gsttype: string;
  gst: string;
  mintype: string;
  status: string;
  gstdate: string;
  gstdate1: string;
}

export interface IReadNCompany {
  id: string;
}

export interface INCompanyTariff {
  companyName: string;
  id: string;
  tid: string;
  vid: string;
  cid: string;
  rate: string;
}

export interface IReadNCompanyTariff {
  id: string;
}

export interface INCompanyRates {
  id: string;
  vid: string;
  cid: string;
  mintype: string;
  initial: string;
  xkm: string;
  xhr: string;
  early: string;
  early_morning: string;
  late: string;
  onite: string;
}

export interface IReadNCompanyRates {
  id: string;
}
