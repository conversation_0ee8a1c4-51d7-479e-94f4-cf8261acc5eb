export interface ISubAgent {
  id: string;
  name: string;
  info: string;
  edate: string;
  mintype: string;
}

export interface IReadSubAgent {
  id: string;
}

export interface ISubAgentTariff {
  subAgentName: string;
  id: string;
  tid: string;
  vid: string;
  cid: string;
  rate: string;
}

export interface IReadSubAgentTariff {
  id: string;
}

export interface ISubAgentRates {
  id: string;
  vid: string;
  cid: string;
  mintype: string;
  initial: string;
  xkm: string;
  xhr: string;
  early: string;
  early_morning: string;
  late: string;
  onite: string;
}

export interface IReadSubAgentRates {
  id: string;
}
