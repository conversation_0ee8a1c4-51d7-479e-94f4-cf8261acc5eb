import { useQuery } from '@tanstack/react-query';

import apiClient from '@constants/axios';

interface AttendanceParams {
  startDate?: string;
  endDate?: string;
}

interface AttendanceStats {
  id: number;
  name: string;
  totalHires: number;
  totalAbsents: number;
}

const fetchDriverAttendance = async (
  params: AttendanceParams
): Promise<AttendanceStats[]> => {
  const formattedParams = {
    startDate: params.startDate,
    endDate: params.endDate,
  };

  const { data } = await apiClient.get('/drivers/attendance-overview', {
    params: formattedParams,
  });
  return data;
};

export const useDriverAttendance = (params: AttendanceParams) =>
  useQuery<AttendanceStats[], Error>({
    queryKey: ['driverAttendance', params],
    queryFn: () => fetchDriverAttendance(params),
    enabled: Boolean(params.startDate) && Boolean(params.endDate),
  });

interface SingleAttendanceDay {
  date: string;
  status: 'Present' | 'Absent';
  hireCount: number;
}

interface SingleDriverAttendanceResponse {
  driverName: string;
  attendance: SingleAttendanceDay[];
}

const fetchSingleDriverAttendance = async (
  driverId: number,
  params: AttendanceParams
): Promise<SingleDriverAttendanceResponse> => {
  const { data } = await apiClient.get(
    `/drivers/single-attendance/${driverId}`,
    {
      params,
    }
  );
  return data;
};

export const useSingleDriverAttendance = (
  driverId: number,
  params: AttendanceParams
) =>
  useQuery<SingleDriverAttendanceResponse, Error>({
    queryKey: ['driverAttendance', driverId, params],
    queryFn: () => fetchSingleDriverAttendance(driverId, params),
    enabled:
      Boolean(driverId) && Boolean(params.startDate) && Boolean(params.endDate),
  });
