import axios from 'axios';

import { useMutation, useQuery } from '@tanstack/react-query';

import apiClient from '@constants/axios';

import { IReadUsers, IUsers } from '../../types/controlpanel/users';

// READ ALL
const fetchUsers = async (): Promise<IUsers[]> => {
  const { data } = await apiClient.get(`/users`);
  return data?.users;
};

export const useReadUsers = () =>
  useQuery<IUsers[], Error>({
    queryKey: ['users', 'list'],
    queryFn: fetchUsers,
  });

// READ ONE
const fetchUser = async (params: IReadUsers): Promise<IUsers> => {
  const { data } = await apiClient.get(`/users/${params.id}`);
  return data;
};

export const useReadUser = (params: IReadUsers) =>
  useQuery<IUsers, Error>({
    queryKey: ['users', 'details', params.id],
    queryFn: () => fetchUser(params),
  });

// useMutation
export const useAddUsers = () => {
  const { mutate } = useMutation({
    mutationKey: ['add-users'],
    mutationFn: (data: any) => axios.post('/users', data),
  });
  return {
    mutate,
  };
};

export const useEditUsers = () => {
  const { mutate } = useMutation({
    mutationKey: ['edit-users'],
    mutationFn: (data: any) => axios.put('/users', data),
  });
  return { mutate };
};

export const useDeleteUsers = () => {
  const { mutate } = useMutation({
    mutationKey: ['delete-users'],
    mutationFn: (data: any) => axios.delete('/users', data),
  });
  return { mutate };
};
