import axios from 'axios';

import { useMutation, useQuery } from '@tanstack/react-query';

import apiClient from '@constants/axios';

import { ICompany, IReadCompany } from '../../types/controlpanel/companies';

// READ ALL
const fetchCompanys = async (): Promise<ICompany[]> => {
  const { data } = await apiClient.get(`/users`);
  return data?.users;
};

export const useReadCompanys = () =>
  useQuery<ICompany[], Error>({
    queryKey: ['company', 'list'],
    queryFn: fetchCompanys,
  });

// READ ONE
const fetchCompany = async (params: IReadCompany): Promise<ICompany> => {
  const { data } = await apiClient.get(`/users/${params.id}`);
  return data;
};

export const useReadCompany = (params: IReadCompany) =>
  useQuery<ICompany, Error>({
    queryKey: ['company', 'details', params.id],
    queryFn: () => fetchCompany(params),
  });

// useMutation
export const useAddCompany = () => {
  const { mutate } = useMutation({
    mutationKey: ['add-company'],
    mutationFn: (data: any) => axios.post('/company', data),
  });
  return {
    mutate,
  };
};

export const useEditCompany = () => {
  const { mutate } = useMutation({
    mutationKey: ['edit-company'],
    mutationFn: (data: any) => axios.put('/company', data),
  });
  return { mutate };
};

export const useDeleteCompany = () => {
  const { mutate } = useMutation({
    mutationKey: ['delete-company'],
    mutationFn: (data: any) => axios.delete('/company', data),
  });
  return { mutate };
};
