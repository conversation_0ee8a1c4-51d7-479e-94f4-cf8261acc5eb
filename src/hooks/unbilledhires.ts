import axios from 'axios';

import { useMutation, useQuery } from '@tanstack/react-query';

import apiClient from '@constants/axios';

import { IReadUnbilledHire, IUnbilledHire } from '../types/unbilledhires';

// READ ALL
const fetchUnbilledHires = async (): Promise<IUnbilledHire[]> => {
  const { data } = await apiClient.get(`/users`);
  return data?.users;
};

export const useReadUnbilledHires = () =>
  useQuery<IUnbilledHire[], Error>({
    queryKey: ['unbilledhires', 'list'],
    queryFn: () => fetchUnbilledHires(),
  });

// READ ONE
const fetchUnbilledHire = async (
  params: IReadUnbilledHire
): Promise<IUnbilledHire> => {
  const { data } = await apiClient.get(`/users/${params.id}`);
  return data;
};

export const useReadUnbilledHire = (params: IReadUnbilledHire) =>
  useQuery<IUnbilledHire, Error>({
    queryKey: ['unbilledhires', 'details', params.id],
    queryFn: () => fetchUnbilledHire(params),
  });

// usemutation
export const useAddUnbilledHire = () => {
  const { mutate } = useMutation({
    mutationKey: ['add-unbilledhires'],
    mutationFn: (data: any) => axios.post('/unbilledhires', data),
  });
  return {
    mutate,
  };
};

export const useEditUnbilledHire = () => {
  const { mutate } = useMutation({
    mutationKey: ['edit-unbilledhires'],
    mutationFn: (data: any) => axios.put(`/unbilledhires`, data),
  });
  return {
    mutate,
  };
};

export const useDeleteUnbilledHire = () => {
  const { mutate } = useMutation({
    mutationKey: ['delete-unbilledhires'],
    mutationFn: (data: any) => axios.delete(`/unbilledhires`, data),
  });
  return {
    mutate,
  };
};
