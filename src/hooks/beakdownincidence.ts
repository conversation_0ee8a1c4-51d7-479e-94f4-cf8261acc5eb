import axios from 'axios';

import { useMutation, useQuery } from '@tanstack/react-query';

import apiClient from '@constants/axios';

import { IReadUnbilledHire, IUnbilledHire } from '../types/unbilledhires';

// READ ALL
const fetchBrakdownIncidences = async (): Promise<IUnbilledHire[]> => {
  const { data } = await apiClient.get(`/users`);
  return data?.users;
};

export const useReadBrakdownIncidence = () =>
  useQuery<IUnbilledHire[], Error>({
    queryKey: ['breakdownincidence', 'list'],
    queryFn: () => fetchBrakdownIncidences(),
  });

// READ ONE
const fetchBrakdownIncidence = async (
  params: IReadUnbilledHire
): Promise<IUnbilledHire> => {
  const { data } = await apiClient.get(`/users/${params.id}`);
  return data;
};

export const useReadBreakdownIncidence = (params: IReadUnbilledHire) =>
  useQuery<IUnbilledHire, Error>({
    queryKey: ['breakdownincidence', 'details', params.id],
    queryFn: () => fetchBrakdownIncidence(params),
  });

// usemutation
export const useAddBrakdownIncidence = () => {
  const { mutate } = useMutation({
    mutationKey: ['add-breakdownincidence'],
    mutationFn: (data: any) => axios.post('/breakdownincidence', data),
  });
  return {
    mutate,
  };
};

export const useEditBrakdownIncidence = () => {
  const { mutate } = useMutation({
    mutationKey: ['edit-breakdownincidence'],
    mutationFn: (data: any) => axios.put(`/breakdownincidence`, data),
  });
  return {
    mutate,
  };
};

export const useDeleteBrakdownIncidence = () => {
  const { mutate } = useMutation({
    mutationKey: ['delete-breakdownincidence'],
    mutationFn: (data: any) => axios.delete(`/breakdownincidence`, data),
  });
  return {
    mutate,
  };
};
