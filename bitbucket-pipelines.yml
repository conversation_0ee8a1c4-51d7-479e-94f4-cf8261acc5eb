image: node:20-alpine

definitions:
  caches:
    pnpm: $BITBUCKET_CLONE_DIR/.pnpm-store

options:
  max-time: 10

pipelines:
  pull-requests:
    '**':
      - step:
          name: Build & Test
          caches:
            - node
            - pnpm
          script:
            - npm install -g pnpm
            - pnpm install --frozen-lockfile
      - parallel:
          - step:
              name: Prettier validate files
              caches:
                - node
                - pnpm
              script:
                - CI=true npm run prettier:check
          - step:
              name: <PERSON>t files
              caches:
                - node
                - pnpm
              script:
                - CI=true npm run lint
          - step:
              name: Type check files
              caches:
                - node
                - pnpm
              script:
                - CI=true npx tsc

  branches:
    develop:
      - step:
          name: Build & Deploy Development
          deployment: development
          caches:
            - node
            - pnpm
          script:
            - npm install -g pnpm
            - pnpm install --frozen-lockfile
            - npx vite build --mode staging
            - pipe: atlassian/sftp-deploy:0.10.0
              variables:
                USER: $FTP_USER
                SERVER: $FTP_HOST
                PASSWORD: $FTP_PASSWORD
                REMOTE_PATH: '/public_html/portal_nunes/'
                LOCAL_PATH: 'dist/*'

      - step:
          name: Build & Deploy Production
          deployment: production
          trigger: manual
          caches:
            - node
            - pnpm
          script:
            - npm install -g pnpm
            - pnpm install --frozen-lockfile
            - npx vite build --mode production
            - pipe: atlassian/sftp-deploy:0.10.0
              variables:
                USER: $FTP_USER
                SERVER: $FTP_HOST
                PASSWORD: $FTP_PASSWORD
                REMOTE_PATH: '/public_html/portal_nunes/'
                LOCAL_PATH: 'dist/*'
