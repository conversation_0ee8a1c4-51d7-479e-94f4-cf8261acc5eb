image: php:8.1-apache

options:
  max-time: 15

pipelines:
  branches:
    develop:
      - step:
          name: Deploy (SFTP) Development
          deployment: development
          script:
            - pipe: atlassian/sftp-deploy:0.10.0
              variables:
                USER: $FTP_USER
                SERVER: $FTP_HOST
                PASSWORD: $FTP_PASSWORD
                LOCAL_PATH: "src/app/*"
                REMOTE_PATH: "/public_html/ci/app/"

            - pipe: atlassian/sftp-deploy:0.10.0
              variables:
                USER: $FTP_USER
                SERVER: $FTP_HOST
                PASSWORD: $FTP_PASSWORD
                LOCAL_PATH: "src/public/*"
                REMOTE_PATH: "/public_html/api_nunes/"

      - step:
          name: Deploy (SFTP) Production
          deployment: production
          trigger: manual
          script:
            - pipe: atlassian/sftp-deploy:0.10.0
              variables:
                USER: $FTP_USER
                SERVER: $FTP_HOST
                PASSWORD: $FTP_PASSWORD
                LOCAL_PATH: "src/app/*"
                REMOTE_PATH: "/public_html/ci/app/"

            - pipe: atlassian/sftp-deploy:0.10.0
              variables:
                USER: $FTP_USER
                SERVER: $FTP_HOST
                PASSWORD: $FTP_PASSWORD
                LOCAL_PATH: "src/public/*"
                REMOTE_PATH: "/public_html/api_nunes/"
