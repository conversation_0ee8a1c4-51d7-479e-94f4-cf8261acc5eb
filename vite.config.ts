import react from '@vitejs/plugin-react';



import path from 'path';
import { defineConfig } from 'vite';


// ----------------------------------------------------------------------

export default defineConfig({
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    sourcemap: true,
  },
  plugins: [react()],
  resolve: {
    alias: [
      {
        find: /^~(.+)/,
        replacement: path.join(process.cwd(), 'node_modules/$1'),
      },
      {
        find: /^src(.+)/,
        replacement: path.join(process.cwd(), 'src/$1'),
      },
      {
        find: /^@pages(.+)/,
        replacement: path.join(process.cwd(), 'src/pages/$1'),
      },
      {
        find: /^@components(.+)/,
        replacement: path.join(process.cwd(), 'src/components/$1'),
      },
      {
        find: /^@constants(.+)/,
        replacement: path.join(process.cwd(), 'src/constants/$1'),
      },
    ],
  },
  server: {
    port: 3000,
  },
});