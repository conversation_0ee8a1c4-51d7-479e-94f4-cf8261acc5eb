{"semi": true, "trailingComma": "es5", "singleQuote": true, "jsxSingleQuote": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "arrowParens": "avoid", "bracketSpacing": true, "endOfLine": "auto", "importOrder": ["^react$", "^[a-z]", "^@mui/(.*)$", "^@material-table/(.*)$", "^@tanstack/(.*)$", "^@pages/(.*)$", "^@components/(.*)$", "^@constants/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "importOrderCaseInsensitive": true, "plugins": ["@trivago/prettier-plugin-sort-imports"]}