services:
  php-apache-environment:
    container_name: php-apache-ci
    build:
      context: ./
      dockerfile: Dockerfile
    ports:
      - 7709:8089
  db:
    container_name: db-ci
    image: mariadb:10.5
    volumes:
      - db_data:/var/lib/mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: mysql
      MYSQL_DATABASE: nunes
      MYSQL_USER: root
      MYSQL_PASSWORD: mysql
    ports:
      - 3310:3306

volumes:
  db_data: {}
