{"name": "nunes-travels-frontend", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=18"}, "scripts": {"dev": "vite --mode develop", "start": "vite preview", "build": "vite build", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier:format": "prettier --write \"src/**/*.{jsx,js,ts,tsx,css,json,css,scss,md}\"", "prettier:check": "npx prettier \"src/**/*.{jsx,js,ts,tsx,css,json,css,scss,md}\" --check", "rm:all": "rm -rf node_modules .next out dist build", "re:start": "yarn rm:all && yarn install && yarn dev", "re:build": "yarn rm:all && yarn install && yarn build", "re:build-npm": "npm run rm:all && npm install && npm run build", "dev:host": "vite --host", "prepare": "husky"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@material-table/core": "^6.4.3", "@mui/icons-material": "^5.15.20", "@mui/lab": "5.0.0-alpha.170", "@mui/material": "^5.15.20", "@tanstack/react-query": "^5.37.1", "@types/node": "^20.12.11", "axios": "^1.6.8", "date-fns": "^3.6.0", "html-react-parser": "^5.1.10", "notistack": "^3.0.1", "react": "^18.3.1", "react-csv": "^2.2.2", "react-dom": "^18.3.1", "react-hook-form": "^7.51.5", "react-router-dom": "^6.23.1", "react-to-print": "^2.15.1", "recharts": "^2.12.7", "to-words": "^4.0.1"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/node": "^20.12.11", "@types/react": "^18.3.1", "@types/react-csv": "^1.1.10", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^18.0.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-perfectionist": "^2.10.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "eslint-plugin-unused-imports": "^3.2.0", "husky": "^9.0.11", "prettier": "^3.2.5", "typescript": "^5.4.5", "vite": "^5.2.11", "vite-plugin-checker": "^0.6.4"}}