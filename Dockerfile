FROM php:8.1-apache
RUN apt-get update && apt-get upgrade -y
RUN apt-get install -y build-essential libssl-dev libzip-dev libpng-dev libjpeg-dev libfreetype6-dev
RUN apt-get install -y libicu-dev
RUN apt-get update
RUN apt-get install -y zip
RUN apt-get install libonig-dev
RUN docker-php-ext-install mysqli zip mbstring
RUN docker-php-ext-install intl
RUN docker-php-ext-configure intl
RUN  curl -sS https://getcomposer.org/installer | php \
  && chmod +x composer.phar && mv composer.phar /usr/local/bin/composer
WORKDIR /var/www/html
COPY ./src ./
RUN composer install --no-scripts --no-autoloader
EXPOSE 8089 7709
RUN bash -c "composer install"
CMD bash -c "php spark serve --host 0.0.0.0 --port 8089"
